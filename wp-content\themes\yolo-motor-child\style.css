/*
Theme Name: <PERSON><PERSON> <PERSON> Child Theme
Theme URI: http://demo.yolotheme.com/motor
Author: YoloTheme
Author URI: http://facebook.com/yolotheme/
Description: This is a custom child theme for Yolo Motor
Template: yolo-motor
Version: 1.0.8
Tags: two-columns, left-sidebar, right-sidebar, fixed-layout, custom-background, custom-header, custom-menu, editor-style, featured-images, flexible-header, full-width-template, sticky-post, theme-options, translation-ready
Text Domain: yolo-motor
*/
/*
.yolo-sticky-wrapper {
    display: none !important;
}
*/
/* FONTS */
@font-face {
    font-family: "AkzidenzGroteskPro";
    src:
      local("AkzidenzGroteskPro");
      src: url('assets/fonts/AkzidenzGroteskPro-Light.eot');
      src: url('assets/fonts/AkzidenzGroteskPro-Light.eot?#iefix') format('embedded-opentype'),
           url("assets/fonts/AkzidenzGroteskPro-Light.woff2") format("woff2"),
           url("assets/fonts/AkzidenzGroteskPro-Light.woff") format("woff");
      font-weight: 300;
}
@font-face {
    font-family: "AkzidenzGroteskPro";
    src:
      local("AkzidenzGroteskPro");
      src: url('assets/fonts/AkzidenzGroteskPro-Md.eot');
      src: url('assets/fonts/AkzidenzGroteskPro-Md.eot?#iefix') format('embedded-opentype'),
           url("assets/fonts/AkzidenzGroteskPro-Md.woff2") format("woff2"),
           url("assets/fonts/AkzidenzGroteskPro-Md.woff") format("woff");
      font-weight: 500;
}
@font-face {
    font-family: "AkzidenzGroteskPro";
    src:
      local("AkzidenzGroteskPro");
      src: url('assets/fonts/AkzidenzGroteskPro-Bold.eot');
      src: url('assets/fonts/AkzidenzGroteskPro-Bold.eot?#iefix') format('embedded-opentype'),
           url("assets/fonts/AkzidenzGroteskPro-Bold.woff2") format("woff2"),
           url("assets/fonts/AkzidenzGroteskPro-Bold.woff") format("woff");
      font-weight: 700;
}
@font-face {
    font-family: "AkzidenzGroteskPro";
    src:
      local("AkzidenzGroteskPro");
      src: url('assets/fonts/AkzidenzGroteskPro-Super.eot');
      src: url('assets/fonts/AkzidenzGroteskPro-Super.eot?#iefix') format('embedded-opentype'),
           url("assets/fonts/AkzidenzGroteskPro-Super.woff2") format("woff2"),
           url("assets/fonts/AkzidenzGroteskPro-Super.woff") format("woff");
      font-weight: 900;
}
@font-face {
    font-family: 'HelveticaNeue';
    src: url('assets/fonts/HelveticaNeueLTPro-Roman.eot');
    src: url('assets/fonts/HelveticaNeueLTPro-Roman.eot?#iefix') format('embedded-opentype'),
         url('assets/fonts/HelveticaNeueLTPro-Roman.woff2') format('woff2'),
         url('assets/fonts/HelveticaNeueLTPro-Roman.woff') format('woff'),
         url('assets/fonts/HelveticaNeueLTPro-Roman.ttf')  format('truetype'),
         url('assets/fonts/HelveticaNeueLTPro-Roman.svg#Helvetica Neue LT Pro') format('svg');
    font-weight: 400;
}
:root {
    --main-color: #ea5734;
    --main-color-hover: #c63f1f;
    --midGray: #969696;
    --midGray: #333;
}
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, main, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-family: 'HelveticaNeue', Arial, helvetica, sans-serif;
    vertical-align: baseline;
    box-sizing: border-box;
    line-height: 25px;
}
/* FOR LOADER ON BUTTONS - ajax */
.bg--loading-small,
.bg--loading {
	position: relative;
}
.bg--loading::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: #ffffffa8;
	z-index: 11;
	display: flex;
	align-items: flex-start;
	justify-content: center;
	text-indent: 20px;
}
.bg--loading::after {
	content: '';
	display: block;
	width: 29px;
	height: 29px;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 12;
	margin-left: -12px;
	margin-top: -12px;
	border-radius: 50%;
	border: 4px solid #000;
	border-top-color: transparent;
	animation: spin 1s infinite linear;
}
.bg--loading.smaller-loading::after {
    width: 30px;
    height: 30px;
    top: 50%;
    z-index: 12;
    margin-left: -15px;
    margin-top: -15px;
    border-radius: 50%;
    border: 5px solid #000;
    border-top-color: transparent;
    animation: spin 1s infinite linear;
}
.bg--loading-small::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: #ffffffeb;
	z-index: 1;
	display: flex;
	align-items: flex-start;
    justify-content: center;
	text-indent: 20px;
}
.bg--loading-small::after {
	content: '';
	display: block;
	width: 25px;
	height: 25px;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 2;
	margin-left: -12px;
	margin-top: -12px;
	border-radius: 50%;
	border: 3px solid #000;
	border-top-color: transparent;
	animation: spin 1s infinite linear;
}
body .btn--loading {
    color: rgba(0,0,0,0) !important;
    transition: none !important;
    position: relative;
}
body .btn.btn--loading {
	color: rgba(0,0,0,0) !important;
	transition: none !important;
	position: relative;
}
body .btn--loading::after {
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -9px;
    margin-top: -9px;
    border-radius: 50%;
    border: 3px solid #fff;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}
body .btn--loading-black::after {
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -9px;
    margin-top: -9px;
    border-radius: 50%;
    border: 3px solid #000;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}
body .btn--loading-small-black {
    color: rgba(0,0,0,0) !important;
    transition: none !important;
    position: relative;
    width: 25px;
    height: 25px;
}
body .btn--loading-small-black::after {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -5px;
    margin-top: -6px;
    border-radius: 50%;
    border: 2px solid #000;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}
body .btn--loading-small-white {
    color: rgba(0,0,0,0) !important;
    transition: none !important;
    position: relative;
}
body .btn--loading-small-white::after {
	content: '';
	display: block;
	width: 13px;
	height: 13px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -5px;
	margin-top: -7px;
	border-radius: 50%;
	border: 2px solid white;
	border-top-color: transparent;
	-webkit-animation: spin 1s infinite linear;
	-moz-animation: spin 1s infinite linear;
	-o-animation: spin 1s infinite linear;
	animation: spin 1s infinite linear;
}
@-webkit-keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@-moz-keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@-ms-keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
.msg-popup {
	position: fixed;
	top: 150px !important;
	left: 50% !important;
	right: auto !important;
	width: auto;
	max-width: 400px;
	z-index: 111111111111;
	padding: 35px 25px;
	color: #fff;
	background: #000;
	border-radius: 0px;
	box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.5);
	line-height: 1.4;
	transition: all 0.35s cubic-bezier(.13,.56,.38,.89) 0s;
	opacity: 0;
	pointer-events: none;
	visibility: hidden;
	transform: translateX(-50%) scale(0.8) !important;
	/* border-radius: 10px; */
}
.msg-popup.danger p { color: #fff }
.msg-popup.warning p { color: #fff; }
.msg-popup.info p { color: #fff; }
.msg-popup.success p { color: #fff; }
.close_popup {
    position: absolute;
    top: 24px;
    right: 20px;
    padding: 0;
    line-height: 1;
    font-size: 20px;
    font-weight: 700;
    cursor: pointer;
    width: 12px;
    height: 12px;
    display: none;
}
.close_popup img {
	filter: brightness(0);
	opacity: 0.6;
}
.close_popup:hover {
	filter: invert(0) !important;
}
.close_popup:hover img {
	filter: brightness(0);
	opacity: 0.6;
}
.msg-popup.showw {
	pointer-events: auto;
	visibility: visible;
	opacity: 1;
	top: 200px !important;
	transform: translateX(-50%) scale(1) !important;
}
.msg-popup p {
	color: #000;
	text-align: center;
	margin: 0;
	font-size: 16px;
	font-weight: 400;
    line-height: 1.4;
    text-transform: capitalize;
}
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: 'AkzidenzGroteskPro', sans-serif !important;
    font-weight: 700 !important;
    line-height: 1.1 !important;
}
table, table *{
    vertical-align: middle;
}
p {
    margin-bottom: 20px;
    line-height: 25px;
    word-break: break-word;
}
select {
	max-width: 100%;
	border: 1px solid #D1D6DC !important;
	background: #fff;
	border-radius: 0 !important;
}
.dataTables_wrapper .dataTables_length {
	float: right;
	position: absolute;
	top: -82px;
	right: 0;
}
textarea {
    resize: vertical;
}
.text-underline {
    text-decoration: underline;
}
.text-uppercase {
    text-transform: uppercase;
}
.text-capitalize {
    text-transform: capitalize;
}
.text-lowercase {
    text-transform: lowercase;
}
.line-height-small {
    line-height: 1.1;
}
.line-height-normal {
    line-height: 1.5;
}
.line-height-big {
    line-height: 1.8;
}
.invert {
    filter: invert(1);
}
a:hover .invert {
    filter: invert(0) !important;
}
ul li {
    line-height: 25px;
}
.bold {
    font-weight: 700;
}
.semibold {
    font-weight: 600;
}
.medium {
    font-weight: 500;
}
.normal {
    font-weight: 400;
}
.gap-0 {
    gap: 0 !important;
}
.gap-1 {
    gap: 10px !important;
}
.gap-2 {
    gap: 20px !important;
}
.gap-3 {
    gap: 30px !important;
}
.gap-4 {
    gap: 40px !important;
}
.gap-5 {
    gap: 50px !important;
}
.m-auto {
    margin: auto;
}
.m-0 {
    margin: 0 !important;
}
.m-1 {
    margin: 10px !important;
}
.m-2 {
    margin: 20px !important;
}
.m-3 {
    margin: 30px !important;
}
.m-4 {
    margin: 40px !important;
}
.m-5 {
    margin: 50px !important;
}
.mt-auto {
    margin-top: auto !important;
}
.mt-0 {
    margin-top: 0;
}
.mt-1 {
    margin-top: 10px !important;
}
.mt-15 {
    margin-top: 15px !important;
}
.mt-2 {
    margin-top: 20px;
}
.mt-25 {
    margin-top: 25px !important;
}
.mt-3 {
    margin-top: 30px !important;
}
.mt-4 {
    margin-top: 40px !important;
}
.mt-5 {
    margin-top: 50px !important;
}
.mt-80 {
    margin-top: 80px !important;
}
.mt-100 {
    margin-top: 100px !important;
}
.mt--100 {
    margin-top: -100px !important;
}
.mb-auto {
    margin-bottom: auto !important;
}
.mb-0 {
    margin-bottom: 0 !important;
}
.mb-05 {
    margin-bottom: 5px !important;
}
.mb-1 {
    margin-bottom: 10px !important;
}
.mb-15 {
    margin-bottom: 15px !important;
}
.mb-2 {
    margin-bottom: 20px !important;
}
.mb-25 {
    margin-bottom: 25px !important;
}
.mb-3 {
    margin-bottom: 30px !important;
}
.mb-4 {
    margin-bottom: 40px !important;
}
.mb-5 {
    margin-bottom: 50px !important;
}
.mb-6 {
    margin-bottom: 60px !important;
}
.mb-80 {
    margin-bottom: 80px !important;
}
.mb-100 {
    margin-bottom: 100px !important;
}
.mb-150 {
    margin-bottom: 130px !important;
}
.ml-auto {
    margin-left: auto !important;
}
.ml-0 {
    margin-left: 0 !important;
}
.ml-05 {
    margin-left: 5px !important;
}
.ml-1 {
    margin-left: 10px !important;
}
.ml-15 {
    margin-left: 15px !important;
}
.ml-2 {
    margin-left: 20px !important;
}
.ml-25 {
    margin-left: 25px !important;
}
.ml-3 {
    margin-left: 30px !important;
}
.ml-4 {
    margin-left: 40px !important;
}
.ml-5 {
    margin-left: 50px !important;
}
.ml-6 {
    margin-left: 60px !important;
}
.ml-10 {
    margin-left: 100px !important;
}
.mr-auto {
    margin-right: auto !important;
}
.mr-0 {
    margin-right: 0 !important;
}
.mr-05 {
    margin-right: 5px !important;
}
.mr-1 {
    margin-right: 10px !important;
}
.mr-15 {
    margin-right: 15px !important;
}
.mr-2 {
    margin-right: 20px !important;
}
.mr-25 {
    margin-right: 25px !important;
}
.mr-3 {
    margin-right: 30px !important;
}
.mr-4 {
    margin-right: 40px !important;
}
.mr-5 {
    margin-right: 50px !important;
}
.mr-6 {
    margin-right: 60px !important;
}
.mx-auto {
    margin-right: auto !important;
    margin-left: auto !important;
}
.mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
}
.mx-05 {
    margin-right: 5px !important;
    margin-left: 5px !important;
}
.mx-1 {
    margin-right: 10px !important;
    margin-left: 10px !important;
}
.mx-15 {
    margin-right: 15px !important;
    margin-left: 15px !important;
}
.mx-2 {
    margin-right: 20px !important;
    margin-left: 20px !important;
}
.mx-3 {
    margin-right: 30px !important;
    margin-left: 30px !important;
}
.mx-4 {
    margin-right: 40px !important;
    margin-left: 40px !important;
}
.mx-5 {
    margin-right: 50px !important;
    margin-left: 50px !important;
}
.my-auto {
    margin-top: auto;
    margin-bottom: auto !important;
}
.my-0 {
    margin-top: 0;
    margin-bottom: 0 !important;
}
.my-05 {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}
.my-1 {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
}
.my-2 {
    margin-top: 20px !important;
    margin-bottom: 20px !important;
}
.my-3 {
    margin-top: 30px !important;
    margin-bottom: 30px !important;
}
.my-4 {
    margin-top: 40px !important;
    margin-bottom: 40px !important;
}
.my-5 {
    margin-top: 50px !important;
    margin-bottom: 50px !important;
}
.my-80 {
    margin-top: 80px !important;
    margin-bottom: 80px !important;
}
.my-100 {
    margin-top: 100px !important;
    margin-bottom: 100px !important;
}
.p-0 {
    padding: 0 !important;
}
.p-1 {
    padding: 10px !important;
}
.p-2 {
    padding: 20px !important;
}
.p-3 {
    padding: 30px !important;
}
.p-4 {
    padding: 40px !important;
}
.p-5 {
    padding: 50px !important;
}
.p-8 {
    padding: 80px !important;
}
.p-10 {
    padding: 100px !important;
}
.pt-0 {
    padding-top: 0 !important;
}
.pt-1 {
    padding-top: 10px !important;
}
.pt-2 {
    padding-top: 20px !important;
}
.pt-25 {
    padding-top: 25px !important;
}
.pt-3 {
    padding-top: 30px !important;
}
.pt-4 {
    padding-top: 40px !important;
}
.pt-5 {
    padding-top: 50px !important;
}
.pt-6 {
    padding-top: 60px !important;
}
.pt-7 {
    padding-top: 70px !important;
}
.pt-100 {
    padding-top: 100px !important;
}
.pt-150 {
    padding-top: 150px !important;
}
.pt-200 {
    padding-top: 200px !important;
}
.pb-0 {
    padding-bottom: 0 !important;
}
.pb-1 {
    padding-bottom: 10px !important;
}
.pb-2 {
    padding-bottom: 20px !important;
}
.pb-25 {
    padding-bottom: 25px !important;
}
.pb-3 {
    padding-bottom: 30px !important;
}
.pb-4 {
    padding-bottom: 40px !important;
}
.pb-5 {
    padding-bottom: 50px !important;
}
.pb-7 {
    padding-bottom: 70px !important;
}
.pb-8 {
    padding-bottom: 80px !important;
}
.pb-100 {
    padding-bottom: 100px !important;
}
.pb-200 {
    padding-bottom: 200px !important;
}
.pl-0 {
    padding-left: 0 !important;
}
.pl-1 {
    padding-left: 10px !important;
}
.pl-2 {
    padding-left: 20px !important;
}
.pl-3 {
    padding-left: 30px !important;
}
.pl-4 {
    padding-left: 40px !important;
}
.pl-5 {
    padding-left: 50px !important;
}
.pl-75 {
    padding-left: 75px !important;
}
.pl-100 {
    padding-left: 100px !important;
}
.pl-120 {
    padding-left: 120px !important;
}
.pl-150 {
    padding-left: 150px !important;
}
.pl-200 {
    padding-left: 200px !important;
}
.pr-0 {
    padding-right: 0 !important;
}
.pr-1 {
    padding-right: 10px !important;
}
.pr-2 {
    padding-right: 20px !important;
}
.pr-3 {
    padding-right: 30px !important;
}
.pr-4 {
    padding-right: 40px !important;
}
.pr-5 {
    padding-right: 50px !important;
}
.pr-75 {
    padding-right: 75px !important;
}
.pr-100 {
    padding-right: 100px !important;
}
.pr-120 {
    padding-right: 120px !important;
}
.pr-150 {
    padding-right: 150px !important;
}
.pr-200 {
    padding-right: 200px !important;
}
.py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}
.py-1 {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}
.py-15 {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
}
.py-2 {
    padding-top: 20px !important;
    padding-bottom: 20px !important;
}
.py-25 {
    padding-top: 25px !important;
    padding-bottom: 25px !important;
}
.py-3 {
    padding-top: 30px !important;
    padding-bottom: 30px !important;
}
.py-4 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
}
.py-5 {
    padding-top: 50px !important;
    padding-bottom: 50px !important;
}
.py-8 {
    padding-top: 80px !important;
    padding-bottom: 80px !important;
}
.py-100 {
    padding-top: 100px !important;
    padding-bottom: 100px !important;
}
.py-200 {
    padding-top: 200px !important;
    padding-bottom: 200px !important;
}
.px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.px-1 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}
.px-15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}
.px-2 {
    padding-left: 20px !important;
    padding-right: 20px !important;
}
.px-3 {
    padding-left: 30px !important;
    padding-right: 30px !important;
}
.px-4 {
    padding-left: 40px !important;
    padding-right: 40px !important;
}
.px-5 {
    padding-left: 50px !important;
    padding-right: 50px !important;
}
.px-6 {
    padding-left: 60px !important;
    padding-right: 60px !important;
}
.px-8 {
    padding-left: 80px !important;
    padding-right: 80px !important;
}
.px-100 {
    padding-left: 100px !important;
    padding-right: 100px !important;
}
.px-120 {
    padding-left: 120px !important;
    padding-right: 120px !important;
}
.px-200 {
    padding-left: 200px !important;
    padding-right: 200px !important;
}
.f-0 {
    font-size: 0 !important;
}
.f-10 {
    font-size: 10px !important;
}
.f-12 {
    font-size: 12px !important;
}
.f-14 {
    font-size: 14px !important;
}
.f-16 {
    font-size: 16px !important;
}
.f-18 {
    font-size: 18px !important;
}
.f-20 {
    font-size: 20px !important;
}
.f-24 {
    font-size: 24px !important;
}
.f-36 {
    font-size: 36px !important;
}
.f-48 {
    font-size: 48px !important;
}
.f-64 {
    font-size: 64px !important;
}
.no-border {
    border: none !important;
}
.left-border {
    border-left: 1px solid #e5e5e5 !important;
}
.top-border {
    border-top: 1px solid #e5e5e5 !important;
}
.right-border {
    border-right: 1px solid #e5e5e5 !important;
}
.bottom-border {
    border-bottom: 1px solid #e5e5e5 !important;
}
.hide,
.d-none {
    display: none !important;
}
.transition1 {
    transition: all 0.25s ease-in-out 0s;
}
.transition2 {
    transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
}
.overflow {
    overflow: hidden;
}
.float-right {
    float: right !important;
}
.float-left {
    float: left !important;
}
.flex-column {
    flex-direction: column !important;
}
.flex-row {
    flex-direction: row !important;
}
.flex {
    display: flex !important;
}
.order-1 {
    order: 1 !important;
}
.order-2 {
    order: 2 !important;
}
.nowrap {
    flex-wrap: nowrap;
}
.h100 {
    height: 100%;
}
.h100vh {
    height: 100vh;
}
.h-auto {
    height: auto !important;
}
.h450 {
    height: 23.45vw;
}
.h360 {
    height: 18.75vw;
}
.d-block {
    display: block;
}
.w100 {
    width: 100%;
}
.aic {
    align-items: center !important;
}
.ail {
    align-items: flex-start !important;
}
.air {
    align-items: flex-end !important;
}
.jcr {
    justify-content: flex-end !important;
}
.jcl {
    justify-content: flex-start !important;
}
.jcsb {
    justify-content: space-between !important;
}
.jcc {
    justify-content: center !important;
}
.dropdown {
    position: relative;
}
.dropdown > .dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	display: block;
	min-width: 220px;
	width: auto;
	z-index: 12;
	background: #fff;
	transition: all 0.15s ease-in-out 0s;
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
	transform: translateY(-10px);
	padding: 15px 10px 10px 10px;
	border: 1px solid #e5e5e5;
	margin-top: 10px;
}
.user-account-item::before {
	content: "";
	position: absolute;
	top: 68%;
	right: 0;
	border: 5px solid rgba(0,0,0,0);
	border-top: 5px solid #aaa;
	z-index: 1;
}
.dropdown > .dropdown-menu.drop-right {
    right: 0;
    left: auto;
}
.dropdown > .dropdown-menu > li {
    padding: 0;
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 0px;
}
.dropdown-menu h4 {
	font-size: 14px !important;
    margin-bottom: 7px;
}
.dropdown > .dropdown-menu > li a.btn {
	justify-content: center;
	padding: 8px;
	text-align: center;
	text-transform: uppercase;
	border-radius: 0;
}
.header-customize-right .dropdown-menu {
	box-shadow: none;
	border-radius: 0;
	border-color: #e3e3e3;
}
.dropdown-menu h4.orange {
	margin-top: 14px;
}
.dropdown > .dropdown-menu > li:last-of-type {
    margin-bottom: 0px;
}
.dropdown > .dropdown-menu > li a {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #000;
    padding: 3px 0px;
    line-height: 1.4;
    transition: all 0.25s ease-in-out 0s;
    white-space: nowrap;
}
.dropdown > .dropdown-menu > li a img {
    margin-right: 10px;
    width: 25px;
    height: 25px;
}
.dropdown > .dropdown-menu > li a:hover {
	color: #cc3f20 !important;
	background: none !important;
}
.dropdown > .dropdown-menu > li a.btn.black-bg:hover {
	background: #333 !important;
	color: #fff !important;
}
.dropdown > .dropdown-menu > li a.btn.orange-bg:hover {
	background: #cc3f20 !important;
	color: #fff !important;
}
.dropdown.opened > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    transform: translateY(0px);
}
.dropdown.opened > .dropdown-button {
    color: #333;
}
.dropdown.opened > .dropdown-button:hover {
    color: #333;
    background: #fff;
}
.dropdown.opened > .panel-button {
    z-index: 13;
}
.white {
    color: #fff !important;
}
.lightGray {
    color: #EDF1F2 !important;
}
.gray {
    color: #e5e5e5 !important;
}
.midGray {
    color: #969696 !important;
}
.darkGray {
    color: #333 !important;
}
.red {
    color: #871b1e !important;
}
.black {
    color: #000 !important;
}
.green {
    color: #2C864F !important;
}
.orange {
    color: #EB5534 !important;
}
.white-bg {
    background-color: #fff !important;
}
.lightGray-bg {
    background-color: #EDF1F2 !important;
}
.gray-bg {
    background-color: #e5e5e5 !important;
}
.midGray-bg {
    background-color: #969696 !important;
}
.darkGray-bg {
    background-color: #333 !important;
}
.red-bg {
    background-color: #871b1e !important;
}
.black-bg {
    background-color: #000 !important;
}
.green-bg {
    background-color: #2C864F !important;
}
.orange-bg {
    background-color: #EB5534 !important;
}
.btn.white-bg {
    color: #333 !important;
    border: 1px solid #e5e5e5 !important;
}
.btn.lightGray-bg {
    color: #333 !important;
}
.btn.gray-bg {
    background: #333 !important;
}
.btn.midGray-bg {
    color: #969696 !important;
}
.btn.darkGray-bg {
    color: #fff !important;
}
.btn.red-bg {
    color: #fff !important;
}
.btn.orange-bg {
    color: #fff !important;
}
.btn-badge.white-bg:hover, body .btn.white-bg:hover, button.white-bg:hover {
    background-color: #000 !important;
    color: #fff !important;
    border-color: #000;
}
.btn-badge.lightGray-bg:hover, .btn.lightGray-bg:hover, button.lightGray-bg:hover {
    background-color: #e5e5e5 !important;
}
.btn-badge.gray-bg:hover, .btn.gray-bg:hover, button.gray-bg:hover {
    background-color: #969696 !important;
}
.btn-badge.midGray-bg:hover, .btn.midGray-bg:hover, button.midGray-bg:hover {
    background-color: #333 !important;
}
.btn-badge.darkGray-bg:hover, .btn.darkGray-bg:hover, button.darkGray-bg:hover {
    background-color: #666 !important;
}
.btn-badge.red-bg:hover, .btn.red-bg:hover, button.red-bg:hover {
    background-color: #680a00 !important;
}
.btn-badge.black-bg:hover, .btn.black-bg:hover, button.black-bg:hover {
    background-color: #969696 !important;
}
.btn-badge.orange-bg:hover, .btn.orange-bg:hover, button.orange-bg:hover {
    background-color: #c44225 !important;
}

.white {
    color: #fff !important;
}
.black {
    color: #000 !important;
}
.orange {
    color: #EB5534 !important;
}
.lightGray-bg {
    background-color: #EDF1F2 !important;
}
.orange-bg {
    background-color: #EB5534 !important;
}
.white-bg {
    background-color: #fff !important;
}
.black-bg {
    background-color: #000 !important;
}
.box450 {
    width: 100%;
    max-width: 450px;
    height: 100%;
}
.btn {
    border-radius: 0 !important;
    height: 40px !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.btn.btn-small {
	height: 25px !important;
	font-size: 12px !important;
}
/* NEW */
footer ul {
    display: flex;
}
footer ul li {
    line-height: 1.4;
    padding-bottom: 0px;
    font-weight: 300;
    font-size: 14px;
}
footer ul li a:hover {
    color: #871b1e;
    text-decoration: underline;
}
footer ul:last-of-type {
    margin-bottom: 0 !important;
}
footer .footer-menu-title {
    font-weight: 600;
    margin-bottom: 30px;
}
.footer-logo {
    width: 250px;
}
.footer-social {
    display: flex;
    align-items: center;
}
.footer-social a:hover {
    opacity: 0.4;
}

.yolo-breadcrumb-wrap {
	text-align: left !important;
}
.site-content-archive-product .archive-product-wrap .product-listing {
    min-height: 50px !important;
}
.woocommerce-shop .yolo-breadcrumb-wrap {
	padding-top: 40px;
}
.yolo-breadcrumb-wrap ul.breadcrumbs {
	padding: 0 10px 0px 0;
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
}
.woocommerce-shop .yolo-page-title-wrap,
.woocommerce-account .yolo-breadcrumb-wrap,
.yolo-filter-categories,
#undefined-yolo-sticky-wrapper {
	display: none !important;
}
header#yolo-header {
    height: 90px;
    width: 100%;
    transition: background-color 0.25s ease-in-out 0s, border 0.25s ease-in-out 0s;
    background: rgb(255, 255, 255);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
	padding: 6px 50px 6px !important;
	height: 70px !important;
}
.logo-slogan {
    color: #000;
    font-family: "AkzidenzGroteskPro";
    font-size: 14px;
    font-weight: 700;
    line-height: 1; /* 170% */
    text-transform: uppercase;
    position: relative;
    top: 5px;
    margin-left: 15px;
    display: inline-flex;
    align-items: center;
}
.logo-slogan .orange {
	line-height: 1;
    margin-left: 3px;
    font-family: "AkzidenzGroteskPro";
}
.max950 {
	max-width: 950px;
	margin: auto;
    margin-top: 60px;
}
.um input[type="submit"].um-button, .um input[type="submit"].um-button:focus {
	background: #eb5534;
	border-radius: 0 !important;
	max-width: 200px !important;
	min-width: 140px !important;
}
.um a.um-button:hover, .um input[type="submit"].um-button:hover {
	background-color: #ca3b1b;
}
.um-col-alt .um-center {
	text-align: left;
}
a.um-link-alt {
	line-height: 1 !important;
	color: #eb5534 !important;
	text-align: left !important;
}
.woocommerce-account .woocommerce-MyAccount-content {
	float: right;
	width: calc(100% - 270px);
	background: #EDF1F2;
	padding: 30px;
	border-top: 6px solid #EB5534;
}
.woocommerce-account .woocommerce-MyAccount-navigation {
	float: left;
	width: 250px;
}
.woocommerce-account .woocommerce-MyAccount-navigation ul {
	list-style: none;
	padding-left: 20px;
}
.woocommerce-account .woocommerce-MyAccount-navigation ul li {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 15px 0 45px;
    font-size: 16px;
    color: #6B6D6E;
}
.woocommerce-account .woocommerce-MyAccount-navigation ul li.is-active {
	color: #EB5534 !important;
    background: rgba(235, 85, 52, 0.13);
}
.woocommerce-notices-wrapper + p {
	font-size: 24px;
}
.woocommerce-notices-wrapper + p + p > a {
    color: var(--main-color) !important;
}
.woocommerce-notices-wrapper + p + p {
	font-size: 16px;
	color: #000;
	width: 70%;
}
.wp-block-woocommerce-cart-order-summary-block {
	background: #EDF1F2;
	padding: 20px 20px 130px;
	font-size: 16px !important;
}
.wp-block-woocommerce-cart-order-summary-totals-block span {
	font-size: 20px;
}
.is-large .wc-block-components-sidebar .wc-block-components-totals-item {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.woocommerce ul#shipping_method li {
	text-align: right;
}
.wc-block-components-button.wp-element-button.wc-block-cart__submit-button.contained {
	background: var(--main-color);
	margin: 0 20px 0;
	width: calc(100% - 40px);
	transform: translateY(-100px);
	color: #fff;
}
body:not(.woocommerce-block-theme-has-button-styles) .wc-block-components-button:not(.is-link):focus {
	box-shadow: none !important;
	box-shadow: none !important;
	outline: none !important;
}
.wp-block-woocommerce-cart-order-summary-shipping-block.wc-block-components-totals-wrapper {
	position: absolute;
	visibility: hidden;
	opacity: 0;
	pointer-events: none;
	left: -999999px;
}
.woocommerce-notices-wrapper strong {
	font-weight: 400 !important;
}
.woocommerce-notices-wrapper + p > a{
	color: #EB5534 !important;
    text-decoration: underline !important;
}
.woocommerce-info {
	border-top-color: rgba(0,0,0,0);
}
body main {
	position: relative;
	padding: 40px 0 !important;
}
.page-title-wrap-bg {
	background: none !important;
    height: 50px !important;
    padding: 0 !important;
}
.woocommerce-page:not(.single-product) .page-title-wrap-bg {
	background: none !important;
    height: auto !important;
    padding: 0 !important;
}
.woocommerce-account .woocommerce-MyAccount-navigation ul {
	list-style: none;
	padding-left: 0px;
}
#edit_list_name {
	background: #fff !important;
}
.page-title-inner .page-title {
    font-family: 'HelveticaNeue', Arial, helvetica, sans-serif !important;
    text-transform: none;
    font-weight: 400 !important;
	color: #000 !important;
}
.woocommerce-MyAccount-navigation-link--request-quote {
	display: none !important;
}
.woocommerce-MyAccount-navigation-link{
    position: relative;
}
.woocommerce-MyAccount-navigation-link::before{
    content: '';
    position: absolute;
    left: 13px;
    top: 14px;
    z-index: 1;
    width: 20px;
    height: 20px;
}
.woocommerce-MyAccount-navigation-link--dashboard::before{background: url(assets/images/dashboard-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--orders::before{background: url(assets/images/orders-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--edit-address::before{background: url(assets/images/addresses-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--edit-account::before{background: url(assets/images/account-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--company-accounts::before{background: url(assets/images/account-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--customer-logout::before{background: url(assets/images/logout-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--wishlist::before{background: url(assets/images/wishlist-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--shopping-lists::before{background: url(assets/images/wishlist-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--dashboard:hover::before{background: url(assets/images/dashboard-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--orders:hover::before{background: url(assets/images/orders-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--edit-address:hover::before{background: url(assets/images/addresses-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--edit-account:hover::before{background: url(assets/images/account-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--company-accounts:hover::before{background: url(assets/images/account-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--customer-logout:hover::before{background: url(assets/images/logout-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--wishlist:hover::before{background: url(assets/images/wishlist-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--shopping-lists:hover::before{background: url(assets/images/wishlist-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--dashboard.is-active::before{background: url(assets/images/dashboard-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--orders.is-active::before{background: url(assets/images/orders-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--edit-address.is-active::before{background: url(assets/images/addresses-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--edit-account.is-active::before{background: url(assets/images/account-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--company-accounts.is-active::before{background: url(assets/images/account-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--customer-logout.is-active::before{background: url(assets/images/logout-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--wishlist.is-active::before{background: url(assets/images/wishlist-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--shopping-lists.is-active::before{background: url(assets/images/wishlist-hover-icon.svg) no-repeat center center;}
.woocommerce-MyAccount-navigation-link--downloads,
.woocommerce-MyAccount-navigation-link--request-quote {
	display: none !important;
}
.woocommerce-notices-wrapper {
	position: fixed;
	bottom: 10px;
	left: 30px;
	z-index: 111;
    display: none;
}
.woocommerce-edit-address  .woocommerce-MyAccount-content > p {
	display: none;
}
.u-columns.woocommerce-Addresses.col2-set.addresses {
	display: flex;
	flex-direction: column;
}
.woocommerce-edit-address .woocommerce-MyAccount-content {
	border: none !important;
	background: #fff;
	padding-top: 0;
}
header.header-1 .fr .header-customize-right {
	display: flex;
	align-items: center;
    padding-top: 10px;
    gap: 15px;
}
.alg-wc-wl-icon-wrapper.thumb-btn-style {
	margin-right: 10px;
}
.alg-wc-wl-icon-wrapper .alg-wc-wl-icon {
	font-size: 25px;
	top: 0;
	position: relative;
	background: url(assets/images/wishlist-icon-header.svg) no-repeat center center / 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 30px;
	height: 30px;
}
.alg-wc-wl-icon-wrapper .alg-wc-wl-counter {
	pointer-events: none;
	background: #eb5534;
	color: #fff;
	border-radius: 50px;
	font-weight: normal;
	font-size: 11px;
	display: flex;
	width: 18px;
	height: 18px;
	line-height: 17px;
	letter-spacing: -1px;
	padding-right: 1px;
	text-align: center;
	top: -8px;
	right: -11px;
	position: absolute;
	align-items: center;
	justify-content: center;
}
.algwcwishlistmodal-overlay {
	background: rgba(0,0,0,.9) !important;
}
.algwcwishlistmodal {
	background: #EDF1F2;
}
.algwcwishlistmodal-container .iziToast-close {
	border: none;
	font-size: 30px;
	line-height: 1;
	padding: 0 9px;
}
.page__btn.page__btn--save.js-algwcwishlistmodal-btn-save-wishlist,
.page__btn.page__btn--create.js-algwcwishlistmodal-btn-create,
.page__btn.page__btn--create.js-algwcwishlistmodal-btn-save {
	background: #EB5533;
	border: none;
	color: #fff;
	padding: 7px 25px;
}
.page__btn.page__btn--save.js-algwcwishlistmodal-btn-cancel {
	display: none;
}
.select-wishlist h2,
.create-wishlist-form h2 {
	text-align: left;
	margin-bottom: 20px;
	font-family: HelveticaNeue !important;
	font-size: 24px;
}
.site-content-single-product .single-product-info .single-product-image-wrap {
	float: left;
	width: 30%;
	padding: 0;
    overflow: visible;
}
.single-product .yolo-page-title-section .yolo-page-title-wrap .content-page-title .page-title-inner {
	display: none;
}
.yolo-page-title-section {
	margin-bottom: 0;
	padding-top: 0;
}
body main.single-product-wrap {
	padding-top: 0 !important;
}
.yolo-breadcrumb-wrap ul.breadcrumbs > li:first-child::before {
    display: none;
}
.yolo-breadcrumb-wrap ul.breadcrumbs > li:first-child {
	padding-left: 0;
}
.yolo-breadcrumb-wrap ul.breadcrumbs > li::before {
	position: absolute;
	left: 0;
	top: 50%;
	margin-top: -4px;
    font-size: 18px;
	content: '/';
	width: auto;
	height: auto;
	background-color: rgba(0,0,0,0);
	line-height: 7px;
    color: #aaa !important;
}
.yolo-breadcrumb-wrap ul.breadcrumbs > li {
    padding: 9px 13px;
}
ul.breadcrumbs li a, ul.breadcrumbs li span {
    line-height: 1.2;
    font-size: 16px;
	font-family: 'AkzidenzGroteskPro', sans-serif !important;
    font-weight: 700;
}
ul.breadcrumbs li a {
	color: #aaa !important;
}
ul.breadcrumbs li span {
    color: #000 !important;
}
.woocommerce-account .addresses .title h3.wcmca_address_title, h3.wcmca_address_title {
	font-size: 24px;
	color: #25262C;
	margin-bottom: 20px;
	font-family: 'HelveticaNeue' !important;
	font-weight: 500 !important;
	display: none;
}
.wcmca_additional_addresses_list_title.wcmca_shipping_addresses_title {
	font-size: 24px !important;
	color: #25262C !important;
	margin-bottom: 20px !important;
    margin-top: 80px !important;
	font-family: 'HelveticaNeue' !important;
	font-weight: 500 !important;
}
.wcmca_action_button_container {
	position: absolute;
	right: 60px;
	margin-top: -100px;
    display: none !important;
}
#wcmca_custom_addresses .u-column2.col-2.woocommerce-Address {
	position: absolute;
	margin-top: 45px !important;
	width: 100%;
	text-align: right;
    display: block;
}
#wcmca_add_new_address_button_shipping {
	float: none;
	display: inline-block;
	background: url(assets/images/add-circle.svg) no-repeat center center;
	font-size: 0;
	margin-top: 10px;
	width: auto !important;
	padding: 0 14px;
	height: 24px;
	width: 24px;
	min-width: 24px;
	margin-right: 10px !important;
	position: relative;
}
#wcmca_add_new_address_button_shipping::before {
	content: 'Add New';
	font-size: 14px !important;
	position: absolute;
	right: 120%;
	color: var(--main-color);
	font-weight: 400;
	white-space: nowrap;
	line-height: 24px;
}
.u-columns.woocommerce-Addresses.col2-set.addresses {
	position: relative;
}
body .button.wcmca_bulk_delete_button {
	color: #000 !important;
	background: #eaeaea !important;
	padding: 12px 15px !important;
	min-width: 10px !important;
	margin-left: 10px !important;
}
.wcmca_address_container h5 {
	font-size: 13px;
	display: none;
}
.col2-set.addresses > .col-2.address {
	margin-top: 0 !important;
}
.wcmcam_address_block_title h3 {
	margin-top: 10px;
	display: none !important;
}
.col-1.address .wcmca_duplicate_address_button {
	position: absolute;
	right: 20px;
	transform: translateY(50px);
}
.u-columns.woocommerce-Addresses.col2-set.addresses .u-column2.col-2.woocommerce-Address {
	display: none;
}
.col-2.address .wcmca_duplicate_address_button {
	position: absolute;
	right: 70px;
	transform: translateY(50px);
}
.woocommerce .col2-set .col-1, .woocommerce-page .col2-set .col-1 {
	position: relative;
}
/* .u-column2.col-2.woocommerce-Address, */
body .wcmcam_address_block_title h3,
body .wcmca_address_title_text_content,
body .wcmca_default_address_badge,
body .class_action_sparator,
body .wcmca_address_title_checkbox.wcmca_address_shipping_title_checkbox {
	display: none !important;
}
.site-content-single-product .single-product-info .summary-product-wrap .product_meta {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 16px;
}
.site-content-single-product .single-product-info .summary-product-wrap {
	padding: 0% 2% 0% 2%;
	width: 50%;
}
.site-content-single-product .single-product-info .summary-product-wrap .product_meta > span {
	display: flex;
	padding: 8px;
}
.site-content-single-product .single-product-info .summary-product-wrap .product_meta label {
	width: 40%;
	min-width: 40%;
    text-transform: none;
}
.site-content-single-product .single-product-info {
	padding: 30px 0px;
	-webkit-box-shadow: none;
	box-shadow: none;
    overflow: visible !important;
}
.algwcwishlistmodal-container .form-field-wrap label {
	text-align: left;
	width: 100%;
	display: none;
}
.algwcwishlistmodal-container .form-field-wrap .form-field {
	width: 100%;
	margin-bottom: 20px;
}
.alg-wc-wl-icon-wrapper .alg-wc-wl-icon.fa-heart::before {
	opacity: 0 !important;
}
.shopping-cart-wrapper {
	top: 0px;
}
.shopping-cart-wrapper .widget_shopping_cart_content .widget_shopping_cart_icon {
	position: relative;
	width: 30px;
	height: 30px;
    display: flex;
    align-items: center;
    /* background: url(assets/images/cart.svg) no-repeat center center; */
}
header.yolo-main-header .yolo-header-nav-above {
	border-bottom: none !important;
}
.shopping-cart-wrapper .widget_shopping_cart_content .widget_shopping_cart_icon > i.wicon {
    display: none;
}
.widget_shopping_cart_icon > i.wicon + span.total {
	top: -4px;
	right: -10px;
}
.my-wishlist .widget_shopping_wishlist_content .my-wishlist-wrapper a.yolo-wishlist {
	width: 30px;
	height: 24px;
	display: flex;
}
.my-wishlist .widget_shopping_wishlist_content .my-wishlist-wrapper a.yolo-wishlist span {
	top: -10%;
}
.pt-05 {
	padding-top: 5px;
}
.user-header.user-name {
	display: block;
	text-transform: uppercase;
	font-weight: 700;
	white-space: nowrap;
	line-height: 1;
	font-family: "AkzidenzGroteskPro", sans-serif;
	font-size: 12px;
	font-weight: 700;
}
.img-fluid.user-flag {
	position: relative;
	top: -2px;
	width: 30px;
}
.header-logo a {
	display: flex;
	align-items: center;
}
header.yolo-main-header .header-customize-item + .header-customize-item {
	top: 6px;
}
.my-wishlist .widget_shopping_wishlist_content .my-wishlist-wrapper a.yolo-wishlist span {
	top: -11px;
}
.yolo-page-title-section {
	margin-bottom: 0;
}
#yolo-content-wrapper {
	background-color: #fff;
	z-index: 1;
	position: relative;
}
/* ADDRESSES */
.woocommerce-address-title.title h3 {
	margin: 0 0 20px !important;
    /* font-family: Helvetica; */
    font-size: 24px;
    font-weight: 400;
    line-height: 27.6px;
    text-align: left;
}
.woocommerce-address-title.title {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.wcmcam_address_block_title {
	margin-bottom: 10px;
	display: block;
	overflow: visible !important;
	position: relative ! important;
}
.woocommerce-account .woocommerce address {
	position: relative;
	line-height: 1.8;
	background: #EDF1F2;
	padding: 25px;
	border-top: 5px solid #ea5734;
	border-radius: 0 !important;
	border-left: none !important;
	border-right: none !important;
	border-bottom: none !important;
}
.gray-panel {
	position: relative;
	line-height: 1.8;
	background: #EDF1F2;
	padding: 25px;
	border-top: 5px solid #ea5734;
	border-radius: 0 !important;
	border-left: none !important;
	border-right: none !important;
	border-bottom: none !important;
}
.woocommerce .col2-set .col-2, .woocommerce-page .col2-set .col-2 {
	margin-top: 20px;
}
.woocommerce-account .addresses .title::after, .woocommerce-account .addresses .title::before {
	display: none;
}
.woocommerce-account .addresses .title .edit {
	float: right;
	color: var(--main-color);
}
/* ACCOUNT */
.woocommerce-edit-account .woocommerce-MyAccount-content {
	border: none;
	background: #fff;
	padding: 0;
}
.woocommerce-EditAccountForm.edit-account fieldset {
	background: #EDF1F2;
	padding: 30px;
	border-top: 6px solid #EB5534;
}
.woocommerce-EditAccountForm.edit-account fieldset legend {
	margin: 0px 0 20px;
	transform: translateY(30px);
	background: rgba(0,0,0,0) !important;
	border: none !important;
	outline: none !important;
}
.user-account-item {
	display: flex;
	align-items: center;
}
.user-header {
	white-space: nowrap;
	font-family: "AkzidenzGroteskPro", sans-serif;
	font-size: 12px;
	font-weight: 700;
	text-transform: uppercase;
	display: block;
	line-height: 1.2;
}
.footer-social .wpb_wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.footer-social .wpb_wrapper .wpb_single_image img {
    width: 30px;
    height: 20px;
    margin-left: 20px;
}
#menu-footer-menu {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #fff;
    list-style: none !important;
    padding: 0;
}
.yolo-footer-wrapper.home-1 li a {
    padding-left: 0;
    margin-bottom: 20px !important;
    margin-right: 25px;
    text-transform: uppercase;
}
.yolo-footer-wrapper.home-1 li a::before {
    display: none !important;
}
.yolo-footer-wrapper.home-1 li a:hover {
    color: #999 !important;
    padding-left: 0 !important;
}
body .yolo-page-title-section + main {
    position: relative;
    padding: 90px 0;
    min-height: 50vh !important;
}
body main {
    position: relative;
    min-height: 66vh !important;
}
.login-container {
    max-width: 950px;
    margin: auto;
    width: 100%;
}
#yolo-content-wrapper {
    background-color: #fff !important;
}
#yolo-wrapper {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    /* height: 100dvh; */
}
#yolo-footer-wrapper {
    margin-top: auto;
}
.customer_login_form_wrap>p {
    display: none;
}
.cart-quantity {
    position: absolute;
    top: -13px;
    right: -15px;
    width: 20px;
    height: 20px;
    background: #eb5534;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    color: #fff;
    margin: 0;
    border-radius: 50%;
    font-family: "AkzidenzGroteskPro", sans-serif;
}
header.header-1 .fl .header-logo {
	height: var(--header_1_height);
	display: flex;
	text-align: left;
	align-items: center;
}
.table-product-image {
	background: #EDF1F2;
	padding: 3px;
	width: 50px;
	height: 44px;
	font-size: 11px;
	line-height: 1.1;
	min-width: 50px;
}
td > .single-shop-item + .custom-button {
	text-align: center;
	margin-bottom: 10px;
}
td > .single-shop-item {
    justify-content: center !important;
}
.filter-row .filter-item select {
	height: 220px;
	min-width: 100px;
	width: 100%;
	padding: 7px 0;
}
.filter-row .filter-item select option {
	padding: 3px 15px;
	font-size: 12px;
}
.table-product-image > img {
	width: 100%;
	height: 38px;
	object-fit: contain;
}
.table-product-title {
    font-family: 'HelveticaNeue';
    font-size: 14px;
    font-weight: 400;
    line-height: 23.8px;
    text-align: left;
    color: #000;
}
.table-product-stock {
    font-family: 'HelveticaNeue';
    font-size: 14px;
    font-weight: 700;
    line-height: 13.8px;
    color: #000;
}
.table-product-description {
    font-family: 'HelveticaNeue';
    font-size: 14px;
    font-weight: 400;
    line-height: 13.8px;
    text-align: left;
    color: #757575;
}
#products-table-wrap {
    position: relative;
    min-width: 200px;
}
#products-table-wrap table.dataTable.stripe tbody tr.odd,
#products-table-wrap table.dataTable.display tbody tr.odd {
	background-color: #fff;
}
#products-table_info {
	display: flex;
	white-space: nowrap;
	align-items: center;
    margin-top: 15px;
    font-family: 'AkzidenzGroteskPro', sans-serif !important;
    font-weight: 400 !important;
    line-height: 1.1 !important;
}
.wc-block-components-product-badge.wc-block-components-product-low-stock-badge {
	display: none !important;
}
#products-table-wrap .sorting_disabled.sorting_asc {
	max-width: 100px !important;
	background: none !important;
}
#products-table_paginate {
	margin-top: 15px;
}
#products-table-wrap .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--main-color);
    color: #fff !important;
    border-radius: 30px;
}
#products-table-wrap .dataTables_wrapper .dataTables_paginate .paginate_button {
	box-sizing: border-box;
	display: inline-block;
	min-width: 27px;
	padding: 0;
	margin-left: 2px;
	text-align: center;
	text-decoration: none !important;
	cursor: pointer;
	color: #333 !important;
	border-radius: 2px;
	height: 27px;
	border: none !important;
}
#products-table {
	width: 100%;
    border-collapse: collapse;
}
#products-table thead {
	border-bottom: 1px solid #aaa;
    background: #EDF1F2;
    font-size: 14px;
    height: 68px;
}
#products-table td, #products-table th{
    border: 1px solid #aaa;
    padding: 6px;
    position: relative;
}
#products-table td {
	vertical-align: top;
	background: #fff !important;
}
#products-table.dataTable.display tbody tr.even > .sorting_1,
#products-table.dataTable.order-column.stripe tbody tr.even > .sorting_1 {
    background-color: #fff;
}
.tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 350px;
    z-index: 11111;
    display: block;
    height: auto;
    background: #ea5734;
    font-size: 12px;
    line-height: 1.5 !important;
    font-style: normal;
    font-weight: 400;
    padding: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    color: #fff;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}
.tooltip-header {
	white-space: nowrap;
	display: flex;
	align-items: center;
	gap: 8px !important;
    line-height: 1 !important;
}
.i-icon {
	width: 15px;
	height: 15px;
    font-size: 11px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: var(--main-color);
	border-radius: 50%;
	color: #fff;
	font-weight: 300;
	font-family: HelveticaNeu;
}
.tooltip-header:hover + .tooltip-content {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    bottom: 115%;
}
#products-table th {
	border-top: none;
	line-height: 1.2;
}
#products-table th:first-child, #products-table th:last-child {
    border-left: none;
    border-right: none;
}
#products-table td:first-child, #products-table td:last-child {
    border-left: none;
    border-right: none;
    background: #fff !important;
}
#products-table .custom-button button {
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    height: 28px;
    text-align: center;
    padding: 0 !important;
    max-width: 105px;
    transform: translateX(-3px);
}
#products-table tr td input[type="checkbox"] {
	line-height: 1 !important;
	margin: 0 2px 8px 2px;
	height: 18px;
	width: 18px;
}
.quantity-wrap {
	display: flex;
	width: 80px;
	margin-left: 10px;
	margin-bottom: 8px;
	align-items: center;
}
.quantity-wrap.not-small {
	width: 130px;
}
.quantity-wrap.not-small .minus, .quantity-wrap.not-small .plus {
	font-size: 16px;
}
.quantity-wrap.not-small * {
	width: 60px;
	padding: 5px !important;
	height: 36px !important;
	margin: 0 !important;
}
.quantity-wrap * {
	width: 26px;
	padding: 5px !important;
	height: 22px !important;
    margin: 0 !important;
}
.quantity-wrap .minus, .quantity-wrap .plus {
    user-select: none;
    cursor: pointer;
}
.cart_list_wrapper  {
    display: none !important;
}
.woocommerce-product-gallery > .alg-wc-wl-btn {
	display: none !important;
}
.product_meta > .sku_wrapper:nth-child(2n+1) {
	background: #EDF1F2;
}
.product-stock-status-wrapper {
	border-top: 1px solid #eaeaea;
	border-bottom: 1px solid #eaeaea;
	margin-bottom: 30px;
}
.site-content-single-product .single-product-info .summary-product-wrap .product_meta > span {
	padding: 8px !important;
}
.quantity-wrap .minus, .quantity-wrap .plus, .quantity-wrap input {
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #D1D6DC;
}
.quantity-wrap .minus:hover, .quantity-wrap .plus:hover {
    background: var(--main-color);
    color: #fff;
}
.quantity-wrap .minus:active,
.quantity-wrap .plus:active,
.quantity-wrap .minus:focus,
.quantity-wrap .plus:focus {
    background: var(--main-color-hover);
    color: #fff;
}
.quantity-wrap .minus, .quantity-wrap .plus {
    background: #F7F9FA;
    color: #000;
    font-size: 14px;
    cursor: pointer;
    line-height: 1 !important;
}
.quantity-wrap .minus {
    border-right: none;
}
.quantity-wrap input {
    background: #fff;
    text-align: center;
}
.quantity-wrap .plus {
    border-left: none;
    color: var(--main-color);
}
/* Filter */
.filter-row {
	display: flex;
	gap: 10px;
}
.filter-row.filter-selects {
	display: flex;
	gap: 10px;
	margin: 40px 0 20px 0;
	justify-content: space-between;
}
.filter-item.filter-ce_approved,
.filter-item.filter-usd_price_code {
	max-width: 100px;
}
.filter-item {
	width: 100%;
}
#product_family, #product_series {
    max-width: 100%;
}
bdi {
	display: flex;
	align-items: center;
}
.filter-row .filter-item select {
    height: 220px;
    min-width: 100px;
    width: 100%;
}
#custom-search {
	max-width: 400px;
	width: 100%;
	border: 1px solid #D1D6DC;
	background: #fff;
}
#apply-filters {
	border: none;
	background: var(--main-color);
	padding: 9px 55px;
	color: #fff;
}
#apply-filters:hover {
	background: var(--main-color-hover);
}
.woocommerce-account .woocommerce header.title h3 {
	font-size: 24px;
	color: #25262C;
	margin-bottom: 20px;
	font-family: 'HelveticaNeue' !important;
	font-weight: 500 !important;
}
.woocommerce-account .addresses .title {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.alg-wc-wl-view-table-container > div[style*="display: flex"] {
	flex-direction: column;
}
.alg-wc-wl-view-table-container .col-20per {
	width: 100%;
	margin-bottom: 5px;
    border: none !important;
}
.alg-wc-wl-view-table-container .alg-wc-wl-tablink {
	color: #000 !important;
	background-color: rgba(0,0,0,0) !important;
	text-align: left !important;
	padding-left: 20px !important;
	border: none !important;
}
.alg-wc-wl-view-table-container .alg-wc-wl-tablink.active {
	background-color: #fff !important;
	border: none !important;
}
/* CUSTOM CHECKBOX */
.custom-check input {
	position: absolute;
	pointer-events: none;
	opacity: 0;
	visibility: hidden;
}
.custom-check {
	position: relative;
}
.custom-check input + label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 16px;
    height: 16px;
    border: 1px solid #D1D6DC;
    border-radius: 0;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}
.custom-check input:checked + label::after {
	content: '×';
	position: absolute;
	font-size: 15px;
	color: var(--main-color);
	top: -1px;
	left: 0;
	width: 16px;
	height: 16px;
	border-radius: 0;
	background-color: rgba(0,0,0,0);
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: 300;
	display: flex;
	align-items: center;
	justify-content: center;
}
.custom-check input + label {
	padding-left: 14px;
	cursor: pointer;
}
.custom-check * {
	line-height: 1.2 !important;
}
/* CUSTOM RADIO */
.custom-radio input {
	position: absolute;
	pointer-events: none;
	opacity: 0;
	visibility: hidden;
}
.custom-radio {
	position: relative;
}
.custom-radio input + label::before {
	content: '';
	position: absolute;
	top: 1px;
	left: 0;
	width: 23px;
	height: 23px;
	border: 1px solid #D1D6DC;
	border-radius: 20px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.3s ease;
}
.custom-radio input:checked + label::after {
	content: '';
	position: absolute;
	font-size: 20px;
	color: var(--main-color);
	top: 8px;
	left: 7px;
	width: 9px;
	height: 9px;
	border-radius: 20px;
	background-color: var(--main-color);
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: 300;
	display: flex;
	align-items: center;
	justify-content: center;
}
.custom-radio input + label {
	padding-left: 32px;
	cursor: pointer;
}
.white-small-title {
    position: relative;
	font-family: 'HelveticaNeue', Arial, helvetica, sans-serif !important;
	text-transform: none;
	font-weight: 400 !important;
	font-size: 16px !important;
	background: #fff;
	padding: 14px 17px;
    margin: -16px;
}
#edit-list-form {
	display: flex;
	align-items: center;
	gap: 20px;
}
.widget_shopping_cart_icon > img + span.total {
	position: absolute;
	top: -4px;
    right: -7px;
	width: 20px;
	height: 20px;
	font-size: 10px;
	text-align: center;
	line-height: 20px;
	background-color: var(--primary_color);
	color: #fff;
	border-radius: 100%;
}
#new-list-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: hsla(0, 0%, 0%, 0.75);
	z-index: 111111111111111111;
	display: flex;
	align-items: center;
	justify-content: center;
}
.new-list-form,
#new-list-form {
	display: block;
	background: #EDF1F2;
	padding: 30px;
	width: 400px;
    position: relative;
}
.new-list-form h2,
#new-list-form h2 {
	font-family: 'HelveticaNeue', Arial, helvetica, sans-serif !important;
	text-transform: none;
	font-weight: 400 !important;
	font-size: 22px;
}
.close_list_popup {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 111;
	line-height: 15px;
	padding: 10px;
	font-size: 30px;
	font-weight: 300 !important;
    cursor: pointer;
}
.shopping-list-icon {
	width: 25px;
	display: flex;
	height: 30px;
	align-items: center;
}
.shopping-list-icon img {
    width: 24px;
}
.site-content-single-product .single-product-info .summary-product-wrap .stock {
	display: none;
}
/* SHOPPING LISTS DROPDOWN */
.add-to-shopping-list {
	float: right;
	width: 40%;
	max-width: 240px;
	height: 38px;
}
.custom-add-to-cart {
	position: relative;
    width: 100%;
    height: 100%;
}
.current-list::after {
	content: '';
	margin-left: 10px;
	border: 4px solid rgba(0,0,0,0);
	border-top: 4px solid var(--main-color);
	margin-top: 5px;
}
.current-list {
	width: 100%;
	height: 100%;
	border: 1px solid var(--main-color);
	color: var(--main-color);
	display: flex;
	align-items: center;
	justify-content: center;
    font-family: HelveticaNeue;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.02em;
    text-align: center;
    cursor: pointer;
}
.shopping-lists-dropdown.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    transform: translateY(0px);
}
.shopping-lists-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    display: flex;
    flex-direction: column;
    min-width: 220px;
    width: 100%;
    z-index: 12;
    background: #fff;
    transition: all 0.15s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transform: translateY(-10px);
    padding: 10px 15px;
    border: 1px solid #D1D6DC;
    margin-top: -2px;
}
.shopping-list-item {
	display: block;
	cursor: pointer;
	font-family: HelveticaNeue;
	font-size: 14px;
	font-weight: 400;
	line-height: 30px !important;
}
.woocommerce-checkout .page-title-inner .page-title,
.woocommerce-cart .page-title-inner .page-title {
	padding-top: 40px;
	padding-bottom: 40px;
}
.woocommerce-checkout .content-page-title,
.woocommerce-cart .content-page-title {
	border-bottom: 1px solid #D1D6DC;
}
.wc_payment_methods.payment_methods.methods,
.wp-block-woocommerce-cart-order-summary-coupon-form-block.wc-block-components-totals-wrapper,
.wp-block-woocommerce-cart-order-summary-heading-block.wc-block-cart__totals-title
.woocommerce-checkout #payment ul.payment_methods,
h3.check-out-title,
.cart-subtotal,
.woocommerce-checkout-info,
.woocommerce-checkout .yolo-breadcrumb-wrap,
.woocommerce-cart .yolo-breadcrumb-wrap {
	display: none;
}
.woocommerce-checkout .entry-content .woocommerce {
	padding: 20px 40px;
	margin-bottom: 50px;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3,
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-billing-fields h3 {
	text-transform: uppercase;
    position: relative;
	border-left: none;
	padding-left: 80px;
	font-family: HelveticaNeue !important;
	font-size: 32px;
	font-weight: 400 !important;
	line-height: 36.8px;
	text-align: left;
	text-transform: capitalize !important;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-billing-fields h3::before {
	content: "1";
	width: 64px;
	height: 64px;
	border-radius: 50%;
	background: var(--main-color);
	position: absolute;
	top: -16px;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3:first-of-type::before {
	content: "2";
	width: 64px;
	height: 64px;
	border-radius: 50%;
	background: var(--main-color);
	position: absolute;
	top: -16px;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3:nth-child(4)::before {
	content: "3";
	width: 64px;
	height: 64px;
	border-radius: 50%;
	background: var(--main-color);
	position: absolute;
	top: -16px;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3.additional_order_title {
    margin-top: 45px;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3.additional_order_title::before {
    content: "3";
	width: 64px;
	height: 64px;
	border-radius: 50%;
	background: var(--main-color);
	position: absolute;
	top: -16px;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.woocommerce-checkout .woocommerce .col2-set .col-2,
.woocommerce-page .col2-set .col-2 {
	margin-top: 10px !important;
	padding-top: 0px !important;
	border-top: none !important;
}
#customer_details {
	display: flex;
	flex-direction: column;
}
.checkout.woocommerce-checkout {
	display: flex;
    align-items: flex-start;
	position: relative;
}
#customer_details {
	padding-bottom: 0;
	border-bottom: none;
	display: flex;
	flex-direction: column;
    padding-right: 40px;
}
#customer_details .col-1,
#customer_details .col-2 {
	width: 100% !important;
}
#order_review_heading {
	position: absolute;
	top: 15px;
	left: calc(100% - 397px);
	margin: 0;
	font-family: HelveticaNeue !important;
	font-size: 32px;
	font-weight: 400 !important;
	line-height: 1;
	text-align: left;
	text-transform: none !important;
	border-left: none !important;
}

#order_review {
	max-width: 400px;
    padding: 60px 20px 20px;
    background: #EDF1F2;
}
.woocommerce form .form-row input.input-text, .woocommerce form .form-row textarea, .woocommerce form .form-row select {
	height: 40px;
	background-color: #fff;
	color: #000 !important;
	font-size: 14px;
	border: 1px solid #D1D6DC;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	color: #444;
	line-height: 40px;
}
.select2-container--default .select2-selection--single {
	background-color: #fff;
	border: 1px solid #D1D6DC;
	border-radius: 0;
}
.select2-container .select2-selection--single {
	box-sizing: border-box;
	cursor: pointer;
	display: block;
	height: 40px;
	user-select: none;
	-webkit-user-select: none;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 38px;
	position: absolute;
	top: 1px;
	right: 1px;
	width: 20px;
}
.woocommerce #payment #place_order, .woocommerce-page #payment #place_order {
	background-color: var(--main-color);
	float: none;
	width: 100%;
	border-radius: 0;
	font-weight: 400;
	margin-top: 20px;
}
#ship-to-different-address {
    display: none !important;
}
.woocommerce-checkout .wcmca_address_selector_container #wcmca_add_new_address_button_shipping,
.woocommerce-checkout .wcmca_address_selector_container label {
	display: none !important;
}
.woocommerce-shipping-fields {
	position: relative;
}
.woocommerce-checkout .wcmca_address_selector_container {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 111;
}
.woocommerce-checkout .wcmca_address_selector_container .wcmca-address-select-menu-container {
	border: none;
	border-bottom: 1px solid #D1D6DC;
}
.woocommerce-billing-fields__field-wrapper {
	display: flex;
    /*flex-direction: row-reverse;*/
	gap: 16px;
}
.woocommerce-billing-fields__field-wrapper p {
	width: 50%;
}
#shipping_company {
	width: 50%;
}
.woocommerce form .form-row-first, .woocommerce form .form-row-last, .woocommerce-page form .form-row-first, .woocommerce-page form .form-row-last {
	width: calc(50% - 8px);
	overflow: visible;
}
.woocommerce table.woocommerce-checkout-review-order-table {
	border: none;
	margin: 0 0 20px 0 !important;
	border-radius: 0;
}
.woocommerce-checkout-review-order-table .product-name {
    display: flex;
    align-items: center;
}

.woocommerce-checkout-review-order-table .product-name img {
    margin-right: 10px;
    max-width: 50px; /* Adjust as needed */
}

.woocommerce-checkout-review-order-table th,
.woocommerce-checkout-review-order-table td {
    text-align: left;
    padding: 10px;
}

.woocommerce-checkout-review-order-table .product-quantity {
    text-align: center;
}

.woocommerce-checkout-review-order-table .product-total {
    text-align: right;
}
.woocommerce table.woocommerce-checkout-review-order-table th {
	width: auto !important;
	font-family: HelveticaNeue !important;
	text-transform: none !important;
	font-weight: 500 !important;
}
.woocommerce-form__label.woocommerce-form__label-for-checkbox.checkbox a {
	color: var(--main-color);
}
.woocommerce-form__label.woocommerce-form__label-for-checkbox.checkbox {
	padding: 0 20px;
}
.woocommerce-terms-and-conditions-wrapper {
	text-align: left;
	margin-bottom: 15px;
}
.woocommerce-cart .is-large.wc-block-cart .wc-block-cart-items td:last-child {
	padding-right: 50px;
    min-width: 190px;
}
.woocommerce-cart .is-large.wc-block-cart .wc-block-cart-items td {
	vertical-align: middle !important;
}
.woocommerce-cart .wc-block-cart-item__remove-link {
	position: absolute;
	right: -184px;
	top: 7px;
	font-size: 0 !important;
	color: red !important;
	text-decoration: none !important;
	background: url(assets/images/trash.svg) no-repeat center center !important;
	width: 14px;
	height: 16px;
}
.wc-block-cart-item__total .wc-block-components-formatted-money-amount {
	white-space: nowrap;
	font-size: 16px;
}
.woocommerce-cart .wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__product .wc-block-cart-item__wrap {
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-start;
}
.woocommerce-cart .wc-block-components-product-metadata .wc-block-components-product-details__available-stock {
	display: none;
}
.wc-block-cart__empty-cart__title.with-empty-cart-icon::before {
    display: none !important;
}
.wc-block-cart__empty-cart__title {
	margin-top: 50px;
	font-size: 30px !important;
	font-family: HelveticaNeue !important;
	font-weight: 400 !important;
}
.editor-styles-wrapper table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__wrap > *, .editor-styles-wrapper table.wc-block-cart-items .wc-block-cart-items__row .wc-block-components-quantity-selector, table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__wrap > *, table.wc-block-cart-items .wc-block-cart-items__row .wc-block-components-quantity-selector {
	margin-bottom: 0;
}
.woocommerce-cart .wc-block-components-product-name {
	width: 300px !important;
	flex: 0 0 300px;
	max-width: 300px !important;
}
.woocommerce-cart .wc-block-cart-item__wrap {
	display: flex;
	align-items: center;
	justify-content: space-between;
    position: relative;
}
.woocommerce-cart .page-title .green {
	font-size: 20px;
	color: #019107 !important;
	display: inline-block;
	margin-left: 12px;
}
.wp-block-woocommerce-cart-order-summary-heading-block.wc-block-cart__totals-title {
	display: none !important;
}
.woocommerce-cart .wc-block-cart-item__wrap * {
	margin-bottom: 0 !important;
}
.woocommerce-cart .page-title-inner .page-title {
    display: flex;
    align-items: first baseline;
}
.empty-cart-link {
	margin-left: auto !important;
	font-size: 14px;
	color: var(--main-color);
}
.woocommerce-privacy-policy-text {
	color: var(--main-color);
	line-height: 1.4 !important;
}
.woocommerce-NoticeGroup.woocommerce-NoticeGroup-checkout {
	position: absolute;
	z-index: 1111;
	top: 100px;
	left: 50%;
	transform: translateX(-50%);
	box-shadow: 0 0 50px 0 rgba(0,0,0,0.2);
}
.woocommerce-NoticeGroup.woocommerce-NoticeGroup-checkout .woocommerce-error {
	margin: 0;
}
.woocommerce form .form-row.woocommerce-invalid .select2-container, .woocommerce form .form-row.woocommerce-invalid input.input-text, .woocommerce form .form-row.woocommerce-invalid select {
	border-color: red;
}
.woocommerce-checkout-review-order-table .product-total * {
	text-align: right;
	width: 100%;
}
.woocommerce-Price-amount.amount bdi {
	font-size: 18px;
    justify-content: flex-end;
}
.woocommerce-checkout-review-order-table .product-quantity {
	text-align: center;
	font-size: 18px;
	font-weight: normal;
}
.entry-content table.woocommerce-checkout-review-order-table ul#shipping_method li {
	margin: 0 !important;
}
.entry-content table.woocommerce-checkout-review-order-table {
	border-collapse: unset !important;
}
.woocommerce table.woocommerce-checkout-review-order-table .woocommerce-shipping-totals.shipping > * {
	border-top: 1px solid #ddd !important;
	border-bottom: 1px solid #ddd !important;
	vertical-align: middle;
}
.woocommerce table.woocommerce-checkout-review-order-table td {
    border-top: none !important;
}
.cart_item .product-name .product-title,
.woocommerce table.woocommerce-checkout-review-order-table tfoot th,
.woocommerce table.woocommerce-checkout-review-order-table td {
	font-size: 16px !important;
}
.wp-block-woocommerce-empty-cart-block .wc-block-grid.wp-block-product-new.wc-block-product-new.has-4-columns,
.wp-block-woocommerce-empty-cart-block .wp-block-heading.has-text-align-center:not(.with-empty-cart-icon) {
	display: none;
}
:root :where(.wp-block-separator.is-style-dots)::before {
	content: "";
}
.u-column1.col-1.woocommerce-Address .edit {
	display: none;
}
.woocommerce-Address-title.title h2 {
	font-family: HelveticaNeue !important;
	font-weight: 400 !important;
}
.wcmca_delete_address_button {
	position: absolute;
	top: 30px;
	right: 20px;
	z-index: 11;
	font-size: 0;
	background: url(assets/images/trash.svg) no-repeat center center;
	width: 20px;
	height: 20px;
	display: none;
}
.wcmca_edit_address_button {
	position: absolute;
	top: 30px;
	right: 50px;
	z-index: 11;
	display: none;
	background: url(assets/images/edit-list-icon.svg) no-repeat center center / 18px;
	font-size: 0;
	width: 20px;
	height: 20px;
}
.col-1.address .wcmca_duplicate_address_button {
	position: absolute;
	right: 20px;
	transform: translateY(50px);
    display: none !important;
}
select[multiple] option:checked,
select[multiple]:focus option:checked {
    background: var(--main-color) !important;
    color: #fff !important;
}
.search-small-loader {
	position: absolute !important;
	bottom: 0;
	width: 45px;
	height: 45px;
	right: 0;
	display: block;
	z-index: 1111;
}
.wc-block-cart-item__image a {
	display: block;
	border: 1px solid #dadada;
}
.wc-block-components-quantity-selector::after {
	border: 1px solid #D1D6DC !important;
	border-radius: 0 !important;
}
.wc-block-components-quantity-selector input.wc-block-components-quantity-selector__input {
	height: 35px !important;
    border-left: 1px solid #D1D6DC !important;
    border-right: 1px solid #D1D6DC !important;
}
.wc-block-components-quantity-selector > .wc-block-components-quantity-selector__button--plus {
    color: var(--main-color) !important;
}
.wc-block-components-quantity-selector > .wc-block-components-quantity-selector__button--plus,
.wc-block-components-quantity-selector > .wc-block-components-quantity-selector__button--minus {
	border-radius: 0 !important;
	width: 35px !important;
    background-color: #F7F9FA !important;
    opacity: 1 !important;
}
.wc-block-components-quantity-selector .wc-block-components-quantity-selector__button:active,
.wc-block-components-quantity-selector .wc-block-components-quantity-selector__button:focus {
	box-shadow: none !important;
	outline: none;
}
.wc-block-components-quantity-selector {
	width: 130px;
}
.wc-block-components-quantity-selector .wc-block-components-quantity-selector__button:disabled {
    box-shadow: none;
    cursor: default;
    opacity: .6;
}
.wc-block-components-quantity-selector > .wc-block-components-quantity-selector__button--minus {
    border-radius: 4px 0 0 4px;
    order: 1;
}
.wc-block-components-quantity-selector .wc-block-components-quantity-selector__button {
    background: none transparent;
    background-color: transparent;
    border: 0;
    box-shadow: none;
    color: currentColor;
    cursor: pointer;
    font-size: .9em;
    font-style: normal;
    font-weight: 400;
    margin: 0;
    margin-bottom: 0px;
    min-width: 30px;
    opacity: .6;
    padding: 0;
    text-align: center;
    text-decoration: none;
}
.wc-block-components-quantity-selector input.wc-block-components-quantity-selector__input {
	-moz-appearance: textfield;
	appearance: textfield;
	background: transparent;
	border: 0;
	box-shadow: none;
	color: currentColor;
	flex: 1 1 auto;
	font-size: 1em;
	font-weight: 600;
	line-height: 1;
	margin: 0;
	min-width: 40px;
	order: 2;
	padding: .4em 0;
	text-align: center;
	vertical-align: middle;
}
.wc-block-components-quantity-selector > .wc-block-components-quantity-selector__button--plus {
	border-radius: 0;
	order: 3;
}
.woocommerce-cart .wc-block-cart-item__wrap * {
    margin-bottom: 0 !important;
}
.wc-block-components-quantity-selector {
    border-radius: 0px;
    box-sizing: content-box;
    display: flex;
    margin: 0 0 .25em;
    margin-bottom: 0.25em;
    position: relative;
    width: 107px;
}
.wc-block-components-quantity-selector::after {
    border: 1px solid hsla(0,0%,7%,.11);
    border-radius: 4px;
    bottom: 0;
    content: "";
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
}
.wc-block-components-quantity-selector::after {
    border: 1px solid #D1D6DC !important;
    border-radius: 0 !important;
}
.wc-block-components-quantity-selector.single-prod input.wc-block-components-quantity-selector__input.h40 {
    height: 40px !important;
}
.h40 {
    height: 40px !important;
}
#upload-section {
	position: relative;
	margin-top: 40px;
}
#download-template {
    position: absolute;
    left: 50%;
    bottom: 50px;
    transform: translateX(-50%);
    z-index: 112;
}
#upload-container {
	width: 100%;
	position: relative;
	background: #fff;
	padding: 55px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}
#upload-container::before {
    content: "";
    position: absolute;
    border: 2px solid #d1d6dc;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
#upload-container.dragOver::before {
	content: "Drop your file here";
	font-size: 24px;
	color: rgb(0 0 0);
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	border: 2px dashed var(--main-color);
	background: #dadada;
	z-index: 11111;
    pointer-events: none;
}
#excel-upload {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	z-index: 11;
}
.product-flash-wrap {
    display: none !important;
}
.ce_approved_badge {
	position: absolute;
	top: 50px;
	left: 20px;
}
.shop_table.shop_table_responsive .flex.flex-column .woocommerce-Price-amount.amount {
	width: 80px;
	text-align: right;
}
.wp-block-woocommerce-cart-order-summary-totals-block {
	border-top: none !important;
	padding-bottom: 16px !important;
	font-size: 16px !important;
}
.woocommerce-orders-table__header.woocommerce-orders-table__header-order-number,
.woocommerce-orders-table__cell.woocommerce-orders-table__cell-order-number {
	text-align: left !important;
}
.woocommerce table.my_account_orders {
	font-size: 14px;
	border: none !important;
}
.woocommerce table.my_account_orders th {
	font-size: 12px;
	color: #222222;
	text-transform: uppercase;
	text-align: center;
	line-height: 2.4;
	padding: 7px 12px;
	font-weight: normal;
	border-bottom-width: 2px;
}
.content-page-title .container {
	padding: 0;
}
.page-title-inner .page-title {
	font-family: 'HelveticaNeue', Arial, helvetica, sans-serif !important;
	text-transform: none;
	font-weight: 400 !important;
	color: #000 !important;
	font-size: 44px !important;
}
.woocommerce table.my_account_orders * {
	vertical-align: baseline;
	line-height: 30px !important;
}
.woocommerce-view-order.woocommerce-page .col2-set .col-2 {
	margin-top: 0px !important;
}
.woocommerce-view-order .woocommerce-MyAccount-content {
	background: #fff;
	padding: 0 0 0 30px;
	border: 0;
}
.order-info h2 {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.order-info {
	margin-bottom: 50px;
}
mark.order-status {
	text-transform: uppercase;
	font-size: 14px;
	background: #ec97291f !important;
	padding: 4px 14px;
}
.order-info h2 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 32px !important;
	margin-bottom: 10px !important;
}
.woocommerce-orders-table.woocommerce-MyAccount-orders.shop_table.shop_table_responsive.my_account_orders.account-orders-table thead {
	background: #fff;
}
.woocommerce-account.woocommerce-orders  .woocommerce-MyAccount-content {
    padding: 15px !important;
}
.afreg_extra_fields fieldset {
	border: none !important;
	padding: 15px 0 0 !important;
}
.um-page.um-page-password-reset .yolo-page-title-wrap .page-title-inner .page-title {
	display: none;
}
.single-shop-item .custom-check input:checked + label::after {
	font-size: 17px;
	top: -2px;
	left: -6px;
	width: 21px;
	height: 21px;
}
table.dataTable.hover tbody tr:hover, table.dataTable.display tbody tr:hover {
	background-color: #fff;
}
.single-shop-item .custom-check input + label::before {
	top: 2px;
	left: -3px;
	width: 16px;
	height: 16px;
	border: 1px solid #aaa;
}
.additional_order_wrap {
    /* margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    background-color: #f9f9f9; */
}
.form-row-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 15px;
}

.form-row-half {
    width: 50%;
}

.form-row-full {
    width: 100%;
}
#products-table-wrap .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
	background: rgba(0,0,0,0);
	border-radius: 40px;
	box-shadow: 0 0 0 1px var(--main-color) inset !important;
}
.woocommerce-table.woocommerce-table--order-details.shop_table.order_details th + td {
	text-align: right;
	font-size: 18px;
}
.shipping-table-my-account th,
.shipping-table-my-account td {
    padding: 14px 0;
    font-weight: normal;
	font-size: 18px;
}
.woocommerce-EditAccountForm.edit-account .gray-panel {
	border: none;
}
.woocommerce-EditAccountForm.edit-account .gray-panel:first-child {
	display: none;
}
.custom-panels-container h3 {
	font-family: HelveticaNeue !important;
	font-weight: 400 !important;
	font-size: 25px;
	color: #25262C;
	margin-bottom: 20px;
}
#products-table bdi, #products-table bdi * {
    justify-content: flex-start;
}
#products-table bdi,
#products-table td {
    line-height: 1.2;
	font-size: 12px;
}
#billing_phone_field input, #billing_email_field input {
    background: #f0f0f0;
    opacity: 0.5;
}
#billing_phone_field, #billing_email_field {
    pointer-events: none;
}
.woocommerce form .form-row textarea {
	min-height: 90px;
}
.wc-block-cart-items .wc-block-cart-item__image span {
	padding: 15px;
	display: flex;
	width: auto;
	height: 100%;
	font-size: 13px;
	align-items: center;
	font-family: Helvetica;
	font-size: 16px;
	font-weight: 700;
	line-height: 18.4px;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
}
.woocommerce-cart table.wc-block-cart-items .wc-block-cart-items__row .wc-block-components-product-name {
	display: inline-flex !important;
	width: auto !important;
	max-width: none !important;
	flex: 0 0 auto;
	font-weight: 700;
	font-size: 16px;
    order: 1;
}
.woocommerce-cart table.wc-block-cart-items .wc-block-cart-items__row .wc-block-components-product-metadata {
	font-size: 16px;
	order: 2;
}
.woocommerce-cart table.wc-block-cart-items .wc-block-components-product-metadata .wc-block-components-product-metadata__description > p {
	font-weight: 300 !important;
}
.woocommerce-cart table.wc-block-cart-items .wc-block-cart-item__prices .wc-block-formatted-money-amount.wc-block-components-formatted-money-amount.wc-block-components-product-price__value {
    font-weight: 700;
}
.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-items__header th {
	font-family: "HelveticaNeue";
	font-size: 18px;
	font-weight: 400 !important;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: #757575;
	text-transform: capitalize;
}
table.wc-block-cart-items .wc-block-cart-items__header .wc-block-cart-items__header-product {
    visibility: visible !important;
}
.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-items__header th:first-child span {color: #fff !important;}
.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-items__header th:first-child span::before {
    content: "Product Details";
	font-family: "HelveticaNeue";
	font-size: 18px;
	font-weight: 400 !important;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: #757575;
	text-transform: capitalize;
    position: absolute;
}
.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-items__header th:nth-child(2) {position: relative;}
.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-items__header th:nth-child(2) span {font-size: 0;color: #fff !important;}
.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-items__header th:nth-child(2) span::before {
    content: "Quantity";
	font-family: "HelveticaNeue";
	font-size: 18px;
	font-weight: 400 !important;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: #757575;
	text-transform: capitalize;
    position: absolute;
    right: 20px;
}
.is-large.wc-block-cart .wc-block-cart-items th:last-child {
	padding-right: 50px !important;
}
.woocommerce-cart table.wc-block-cart-items .wc-block-cart-item__prices .wc-block-formatted-money-amount.wc-block-components-formatted-money-amount.wc-block-components-product-price__value::before {
	content: "LIST PRICE: ";
}
.woocommerce-cart table.wc-block-cart-items .wc-block-components-product-details.wc-block-components-product-details {
	display: none;
}
.woocommerce-cart table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__prices {
	font-size: 16px;
	order: 3;
    text-transform: uppercase;
}
.wc-block-cart-item__quantity {
	position: absolute;
	top: 50%;
	right: 0;
	transform: translateY(-50%);
}
.white-small-title .btn.btn-small {
    height: 40px !important;
    position: absolute;
    right: 10px;
    width: 80px;
    top: 6px;
}
.shop_table_responsive .table-product-image {
    width: 56px;
}
.shop_table_responsive .add-to-cart-button {
	font-size: 16px;
	margin-top: 5px;
	padding-right: 25px !important;
	padding-left: 25px !important;
}
.desktop {display: block !important;}
.desktop-flex {display: flex !important;}
.mobile {display: none !important;}
.mobile-flex {display: none !important;}
.wc-block-cart-item__image {
	padding-left: 0 !important;
}
.wc-block-cart-item__image a {
	min-height: 80px;
}
#order_review::before {
	content: "Order Summary";
	position: absolute;
	top: 21px;
	font-size: 32px;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #order_review_heading {
	display: none;
}
#order_review {
	max-width: 400px;
	padding: 60px 20px 20px;
	background: #EDF1F2;
	position: sticky;
	top: 10px;
}
.woocommerce-account .page-title-inner.block-center h1 {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.woocommerce table.my_account_orders thead {
	background: #fff;
}
.disabledd {
	opacity: 0.4;
	pointer-events: none;
}
[data-tooltip]:hover::before {
	transform: translateY(-10px);
    opacity: 1;
}
[data-tooltip]::before {
	content: attr(data-tooltip);
    pointer-events: none;
	opacity: 0;
	transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
	background: #000;
	color: #fff;
	position: absolute;
	right: 0;
	padding: 6px;
	top: -25px;
	transform: translateY(0%);
	white-space: nowrap;
	font-family: "HelveticaNeue";
	font-size: 12px;
	font-weight: 400 !important;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	line-height: 1.4;
}
.position-relative {
    position: relative;
}
.sku_wrapper ins span bdi {
    font-size: 16px !important;
}
.sku_wrapper del,
.shopping-list-single-item del {
    display: none;
}
ins {
	text-decoration: none !important;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields p label {
	font-weight: bold;
	font-size: 14px;
	margin-bottom: 5px;
	color: var(--text_color);
	display: flex;
	position: relative;
	align-items: center;
}
.woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields p label span.i-icon {
	order: 3;
	margin-left: 8px;
    position: relative;
}
#products-table del {
	display: none;
}
.woocommerce-shop main {
	padding-top: 10px !important;
}
.shipping-table-my-account td {
	text-align: right !important;
}
#loginform .login-password input,
#loginform .login-username input {
    width: 100% !important;
    background: #fff;
    color: #000;
    font-size: 16px;
}
#loginform .login-password label, #loginform .login-username label {
	display: none;
}
#loginform .login-submit button {
	background: #eb5534;
	border-radius: 0;
	font-size: 16px;
	font-weight: 400;
	font-family: 'HelveticaNeue';
	width: 120px;
	color: #fff;
	height: 40px;
	border: none;
}
.wplf-lostpassword {
	color: var(--main-color);
	margin-top: 20px !important;
	display: inline-block;
}
.woocommerce button.button {
	background: #eb5534;
	border-radius: 0;
	font-size: 16px;
	font-weight: 400;
	font-family: 'HelveticaNeue';
}
.woocommerce-ResetPassword.lost_reset_password {
	background: #EDF1F2 !important;
	padding: 30px;
	max-width: 500px;
}
.woocommerce-ResetPassword.lost_reset_password .woocommerce-form-row.woocommerce-form-row--first.form-row.form-row-first label {
    display: none;
}
.woocommerce-ResetPassword.lost_reset_password .woocommerce-form-row.woocommerce-form-row--first.form-row.form-row-first,
.woocommerce-ResetPassword.lost_reset_password .woocommerce-form-row.woocommerce-form-row--last.form-row.form-row-last {
	width: 100% !important;
}
.woocommerce-ResetPassword.lost_reset_password .woocommerce-form-row.woocommerce-form-row--first.form-row.form-row-first input {
	width: 100% !important;
    background: #fff;
}
.woocommerce-ResetPassword.lost_reset_password p:first-child {
	margin-bottom: 2px !important;
}
.woocommerce-lost-password .entry-content .woocommerce-message::before {
    display: none;
}
.woocommerce-lost-password .entry-content .woocommerce-message {
	border: none !important;
	padding: 80px 50px;
	font-size: 16px;
}
.woocommerce-lost-password .entry-content {
	max-width: 500px;
}
:focus-visible {
	outline: 0 !important;
}
.single-product .yolo-breadcrumb-wrap ul.breadcrumbs li:not(:first-child):not(:last-child) {
	display: none;
}
.woocommerce-table.woocommerce-table--order-details.shop_table.order_details thead tr th:last-child {
	text-align: right;
}
.yolo-header-nav-above .container {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.dash-text{
    font-size: 16px;
    color: #000;
}
#products-table .disabledd {
	opacity: 1;
	pointer-events: auto;
}
#billing_phone_field .optional {
	display: none;
}
#wcmca_shipping_is_default_address_field {
	padding-left: 20px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	line-height: 26px !important;
}
.select2-container--default .select2-selection--single {
	border: 1px solid #D1D6DC !important;
}
.select2-container .select2-dropdown, .select2-container .select2-selection {
	border-color: #D1D6DC !important;
}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
	background-color: #eb5534 !important;
	color: white !important;
}
.select2-container .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__clear,
.select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 40px !important;
}
.shipping_address .select2-container--default .select2-selection--single .select2-selection__rendered {
	min-width: 190px !important;
}
.shipping_address span.select2-container {
	height: 40px;
}

/* RESPONSIVE */
@media (min-width: 1320px) {
    .container {
        width: 1400px;
    }
}
@media (max-width: 1280px) {
    .md {
        display: none !important;
    }
}
@media screen and (max-width: 991px) {
    .desktop {display: none !important;}
    .desktop-flex {display: none !important;}
    .mobile {display: block !important;}
    .mobile-flex {display: flex !important;}

    .flex-mob-column {
        flex-direction: column;
    }
    .jcsb-mob {
        justify-content: space-between !important;
    }
    .ail-mob {
        align-items: flex-start !important;
    }
    .px-mob-2 {
        padding-right: 20px !important;
        padding-left: 20px !important;
    }
    .woocommerce-account .page-title-inner .page-title {
        font-size: 32px !important;
    }
    .ml-mob-0 {
        margin-left: 0 !important;
    }
    .mt-mob-3 {
        margin-top: 30px !important;
    }
    .mt-mob-0 {
        margin-top: 0px !important;
    }
    .mt-mob-1 {
        margin-top: 10px !important;
    }
    .mt-mob-2 {
        margin-top: 20px !important;
    }
    .mt-mob-3 {
        margin-top: 30px !important;
    }
    .mb-mob-0 {
        margin-bottom: 0px !important;
    }
    .mb-mob-1 {
        margin-bottom: 10px !important;
    }
    .mb-mob-2 {
        margin-bottom: 20px !important;
    }
    .mb-mob-3{
        margin-bottom: 30px !important;
    }
    .w100-mob {
        width: 100% !important;
    }
    footer .flex-mob-column {
        align-items: flex-start !important;
    }
    .f-14 {
        font-size: 12px !important;
    }
    footer ul li {
        padding-bottom: 5px;
    }
    .footer-logo {
        width: 170px;
        margin-bottom: 20px;
    }
    footer .row-mob-reverse {
        flex-direction: column-reverse !important;
        display: flex;
        align-items: flex-start;
    }

    .header-logo-mobile img {
        padding-top: 0;
        padding-bottom: 0;
        max-width: 185px;
    }
    .header-logo-mobile a {
        display: flex;
        flex-direction: column;
        padding: 25px 0;
    }
    header.yolo-mobile-header .yolo-mobile-header-inner {
        max-height: 90px;
        height: 100%;
        background: #fff;
        z-index: 1000;
    }
    .logo-slogan {
        margin: 0;
        font-size: 10px;
        top: 4px;
    }
    header.header-mobile-4 .yolo-mobile-header-inner .toggle-icon-wrapper {
        right: 15px;
        left: auto;
        width: 55px;
        height: 40px;
        background: #eb5534;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 27px;
    }
    header.yolo-mobile-header .toggle-icon-wrapper .toggle-icon > span, header.yolo-mobile-header .toggle-icon-wrapper .toggle-icon::before, header.yolo-mobile-header .toggle-icon-wrapper .toggle-icon::after {
        background-color: #fff;
    }
    .toggle-icon-wrapper.in .toggle-icon::after, .toggle-icon-wrapper.in .toggle-icon::before {
        left: 8px;
    }
    .box450 {
        width: 100%;
        max-width: 100%;
        padding: 20px !important;
    }
    .box450 h3 {
        margin: 10px 0 20px !important;
    }
    .max950 {
        margin-top: 0;
    }
    header.yolo-mobile-header {
        background: #fff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08) !important;
        z-index: 2;
        position: fixed;
        width: 100%;
        z-index: 111;
        top: 0;
        left: 0;
    }
    body {
        padding-top: 90px !important;
    }
    .woocommerce-account .woocommerce-MyAccount-navigation,
    .woocommerce-account .woocommerce-MyAccount-content {
        float: none;
        width: 100%;
    }
    .woocommerce-account .woocommerce-MyAccount-navigation ul {
        padding-left: 0;
    }
    header.header-mobile-4 .yolo-mobile-header-wrapper .header-customize {
        right: 96px;
        left: auto;
        top: 22px;
    }
    .widget_shopping_cart_icon img {
        width: 24px !important;
        height: 24px !important;
    }
    .cart-quantity {
        position: absolute;
        top: -8px;
        right: -9px;
    }
    header.header-mobile-4 .header-logo-mobile {
        width: 200px;
    }
    .logo-slogan .orange {
        line-height: 1;
        font-family: "AkzidenzGroteskPro";
        font-weight: 700;
    }
    #yolo-nav-mobile-menu .header-logo-mobile {
        margin-left: 20px !important;
    }
    .page-title-inner .page-title {
        padding-top: 45px;
    }
    .woocommerce-account .woocommerce-MyAccount-navigation ul {
        margin-bottom: 25px;
    }
    .woocommerce-notices-wrapper + p + p {
        font-size: 16px;
        color: #000;
        width: 100%;
    }
    .table_responsive {
        overflow-x: auto;
    }
    .table_responsive * {
        white-space: nowrap;
    }
    #create-new-list {
        width: 100%;
        margin-top: 20px !important;
    }
    .woocommerce-account .woocommerce-MyAccount-content {
        padding: 25px 20px;
    }
    .white-small-title {
        margin: -10px -5px 20px !important;
        position: relative;
    }
    .new-list-form, #new-list-form {
        width: 92%;
    }
    .shop_table.shop_table_responsive .flex.flex-column .woocommerce-Price-amount.amount {
        margin-left: auto !important;
    }
    .quantity-wrap.not-small {
        width: 130px;
        margin-left: 0;
    }
    .shop_table_responsive .add-to-cart-button {
        font-size: 14px;
        margin-top: 0;
    }
    .wcmca_address_container {
        margin-right: 0 !important;
        margin-bottom: 20px !important;
    }
    .wcmca_save_address_button_container .woocommerce button.button {
        font-weight: normal;
        border-radius: 0;
        padding: 15px 25px;
        background-color: var(--main-color);
        border-radius: 0;
        width: 100%;
        text-transform: uppercase;
        letter-spacing: 0.5px !important;
    }
    .wcmca_additional_addresses_list_title.wcmca_shipping_addresses_title {
        font-size: 20px !important;
        margin-bottom: 5px !important;
        margin-top: 40px !important;
    }
    #wcmca_custom_addresses .u-column2.col-2.woocommerce-Address {
        margin-top: 30px !important;
    }
    #wcmca_add_new_address_button_shipping {
        margin-right: -6px !important;
    }
    #wcmca_add_new_address_button_shipping::before {
        font-size: 12px !important;
        right: 105%;
    }
    .woocommerce-account .woocommerce h2 {
        font-size: 20px;
    }
    .custom-panels-container {
        flex-direction: column;
    }
    .custom-panels-container h3 {
        font-size: 20px;
        margin-bottom: 15px;
    }
    .gray-panel {
        padding: 20px;
    }
    .woocommerce form .form-row label {
        line-height: 1.4;
    }
    .gray-panel legend {
        margin-bottom: 20px;
    }
    .yolo-mobile-header-nav {
        background: #000;
    }
    input[type="text"], input[type="search"], input[type="email"], input[type="url"], input[type="password"], input[type="tel"], textarea, select {
        padding: 0 10px;
    }
    .filter-row.filter-selects {
        flex-direction: column;
    }
    .filter-item.filter-ce_approved, .filter-item.filter-usd_price_code, #product_family, #product_series {
        max-width: 100%;
    }
    .right-border.desktop {
        display: none;
    }
    .clear_search_results {
        position: absolute;
        top: -40px;
        right: 0;
        z-index: 1;
    }
    .post-type-archive .yolo-breadcrumb-wrap {
        text-align: center;
        position: relative;
        z-index: 1;
        margin-top: -30px;
        margin-bottom: -34px;
    }
    #brand {
        height: 150px;
    }
    #ce_approved {
        height: 60px;
    }
    .filter-row .filter-item select {
        height: 200px;
    }
    .yolo-mobile-header-nav.menu-drop-fly.in {
        top: 83px;
    }
    #yolo-nav-mobile-menu .dropdown-menu {
        position: relative;
        top: auto;
        left: auto;
        width: 100%;
        border-radius: 0 !important;
        margin: 0;
        padding: 15px;
        box-shadow: none !important;
        border: none !important;
        z-index: 11;
        display: block !important;
    }
    .yolo-mobile-header-nav.menu-drop-fly {
        display: block;
        position: fixed;
        margin: 0;
        top: -100%;
        bottom: auto;
        overflow: auto;
        overflow-x: hidden;
        width: 100%;
        left: 0;
        border: none !important;
        z-index: 999;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
        background: #fff !important;
        box-shadow: 0 6px 10px 0 rgba(0,0,0,0.2) !important;
    }
    body .yolo-mobile-menu-overlay,
    body.menu-mobile-in .yolo-mobile-menu-overlay {
        display: none !important;
    }
    .dropdown-menu > li > a.btn {
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        text-transform: uppercase;
    }
    .dropdown-menu > li > a {
        padding: 6px 0px 6px 0px !important;
    }
    .table-product-image {
        white-space: normal !important;
    }
    table.dataTable {
        margin: 0;
    }
    /*quick order*/
    #quick-order-container {
        padding: 25px 15px;
    }
    .quick-order-options {
        flex-direction: column;
        align-items: flex-start !important;
    }
    .quick-order-options .custom-radio {
        margin: 0 0 10px !important;
        border: none !important;
    }
    #material-desc-search {
        font-size: 14px;
    }
    #quick-order-container h2 {
        margin-bottom: 20px;
    }
    #quick-order-container .product-suggestion {
        gap: 10px !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        margin-bottom: 5px !important;
    }
    .flex.mob-elipsis {
        width: 100%;
        gap: 10px !important;
    }
    .mob-elipsis .descc {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-right: 20px;
    }
    .product-stock {
        font-size: 14px;
    }
    .quick-order-subtotal.f-20 {
        font-size: 18px !important;
    }
    #empty-cart-button {
        font-size: 14px !important;
    }
    .msg-popup {
        width: 90%;
    }
    .wc-block-cart-item__image a img {
        height: 80px;
        object-fit: contain;
    }
    .wc-block-cart-item__image a {
        height: 80px;
        min-width: 98px;
        margin-right: 10px !important;
        overflow: hidden;
        background: #fff;
    }
    .wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-item__quantity {
        top: 126px;
        left: -102px;
        width: calc(100vw - 40px);
    }
    .is-mobile table.wc-block-cart-items thead {
        display: none;
    }
    .wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-item__quantity::before {
        content: 'Quantity';
        font-weight: 400;
        top: 146px;
        left: -102px;
        font-family: Helvetica;
        font-size: 16px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #757575;
        transform: translateY(-5px) !important;
        display: block;
    }
    .wc-block-components-main.wc-block-cart__main.wp-block-woocommerce-cart-items-block .wc-block-cart-items.wp-block-woocommerce-cart-line-items-block tr {
        display: flex;
        flex-wrap: nowrap;
        position: relative;
    }
    .wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-item__image {
        min-width: 102px !important;
        max-width: 102px;
        border: none !important;
    }
    .is-mobile table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__product {
        border: none !important;
    }
    .is-mobile table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__total .price.wc-block-components-product-price {
        position: absolute;
        top: 7px;
        right: 50%;
        transform: translateX(100%);
        z-index: 1;
    }
    .is-mobile table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__total .price.wc-block-components-product-price::before {
        content: "Total";
        position: absolute;
        top: calc(-100% - 6px);
        font-family: "HelveticaNeue";
        font-size: 16px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #757575;
    }
    .is-mobile table.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block .wc-block-cart-item__image a::before {
        content: "Product Details";
        position: absolute;
        top: 4px;
        font-family: "HelveticaNeue";
        font-size: 16px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #757575;
    }
    .is-mobile table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__total {
        position: absolute;
        top: 71%;
        width: 100%;
        left: 0;
        height: 55px !important;
        border-top: none !important;
        border-bottom: 1px solid #eee !important;
    }
    .is-mobile table.wc-block-cart-items .wc-block-cart-items__row {
        padding: 40px 0 100px 0 !important;
    }
    .woocommerce-cart table.wc-block-cart-items .wc-block-components-product-metadata .wc-block-components-product-metadata__description > p {
        font-weight: 300 !important;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 90%;
        white-space: nowrap;
        /* padding-left: 10px; */
        text-indent: 10px;
    }
    .woocommerce-cart table.wc-block-cart-items .wc-block-cart-items__row .wc-block-components-product-name,
    .woocommerce-cart table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__prices {
        padding-left: 10px;
    }
    .woocommerce-cart .wc-block-cart-item__remove-link {
        right: 0 !important;
        top: 35px;
    }
    .wc-block-cart .wc-block-cart__submit-container--sticky {
        position: relative !important;
    }
    .wp-block-woocommerce-cart-order-summary-block {
        background: #EDF1F2;
        padding: 20px 20px 150px;
        font-size: 16px !important;
    }
    .woocommerce-privacy-policy-text {
        transform: translateY(0px) !important;
    }
    .woocommerce-cart .woocommerce-privacy-policy-text {
        transform: translateY(-140px) !important;
    }
    .wc-block-components-button.wp-element-button.wc-block-cart__submit-button.contained {
        transform: translateY(-120px);
    }
    .is-small .wc-block-cart__sidebar {
        margin-bottom: 0;
    }
    .wc-block-cart__submit {
        height: 0;
    }
    .wc-block-components-button.wp-element-button.wc-block-cart__submit-button.contained {
        transform: translateY(-120px);
    }
    /* CHECKOUT */
    .checkout.woocommerce-checkout {
        flex-direction: column;
    }
    #customer_details {
        padding-right: 0;
    }
    .woocommerce-checkout .entry-content .woocommerce {
        padding: 20px 0px;
        margin-bottom: 0;
    }
    .woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-billing-fields h3 {
        font-size: 24px;
    }
    .woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-billing-fields h3::before {
        width: 44px;
        height: 44px;
        top: -8px;
    }
    .woocommerce-billing-fields h3 {
        margin-bottom: 25px;
    }
    .woocommerce-billing-fields__field-wrapper {
        display: flex;
        flex-direction: column-reverse;
        gap: 0;
    }
    .woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3::before {
        width: 44px;
        height: 44px;
        top: -8px;
    }
    .woocommerce-checkout .wcmca_address_selector_container {
        position: relative;
        top: auto;
        right: auto;
        z-index: 111;
    }
    .woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3, .woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-billing-fields h3 {
        padding-left: 55px;
    }
    #shipping_company {
        width: 100%;
    }
    #order_review_heading {
        position: relative;
        top: auto;
        left: auto;
        margin: 0;
        font-family: HelveticaNeue !important;
        font-size: 24px;
        font-weight: 400 !important;
        line-height: 1;
        text-align: left;
        text-transform: none !important;
        border-left: none !important;
        transform: translateY(46px);
    }
    .form-row-row {
        gap: 0;
        flex-direction: column;
    }
    .woocommerce-checkout .entry-content .woocommerce .woocommerce-checkout #customer_details .woocommerce-shipping-fields h3.additional_order_title::before {
        content: "3";
        top: 4px;
    }
    .woocommerce-order-received.woocommerce-checkout .entry-content .woocommerce {
        padding: 0px 0px;
        margin-bottom: 0;
    }
    body .thankyou-message {
        padding: 50px 20px !important;
    }
    body .thankyou-message h2 {
        font-size: 36px !important;
    }
    body .thankyou-message p {
        font-size: 16px !important;
    }
    .woocommerce-shop main {
        padding-top: 40px !important;
    }
}
@media(max-width: 767px) {
    .woocommerce-lost-password .woocommerce button.button {
        width: 100%;
        margin-top: 10px;
    }
    .site-content-single-product .single-product-info .summary-product-wrap,
    .site-content-single-product .single-product-info .single-product-image-wrap {
        width: 100%;
    }
    .add-to-shopping-list {
        width: 100%;
        max-width: 100%;
        margin-top: 20px;
    }
    .site-content-single-product .single-product-info .woocommerce-Price-amount.amount bdi {
        font-size: 14px;
    }
    .site-content-single-product .single-product-info .summary-product-wrap .product_meta {
        font-size: 14px;
    }
    .site-content-single-product .single-product-info .summary-product-wrap .product_meta label {
        line-height: 1.3;
    }
    .site-content-single-product .single-product-info {
        padding: 10px 0px;
    }
    .single-product .page-title-wrap-bg {
        height: 35px !important;
    }
    #products-table-wrap .top {
        display: none;
    }
    .is-mobile table.wc-block-cart-items, .is-small table.wc-block-cart-items {
        max-width: calc(100vw - 20px) !important;
    }
    .woocommerce-cart table.wc-block-cart-items .wc-block-cart-items__row .wc-block-components-product-metadata {
        max-width: 65vw;
    }
    .select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[data-selected] {
        background-color: #eb5534;
        color: #fff;
    }
    .woocommerce-account .woocommerce .order-info h2 {
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        gap: 20px;
    }
    .woocommerce-column.woocommerce-column--1.woocommerce-column--billing-address.col-1 {
        margin-bottom: 20px;
    }
	#phone_field {
    display: none !important;
}
}

/* Overrides to tighten filter row spacing and remove large gaps between columns */
.filter-row.filter-selects {
    justify-content: flex-start !important;
    flex-wrap: wrap !important;
}
.filter-row.filter-selects .filter-item {
    flex: 1 1 220px; /* allow items to shrink/grow and wrap */
    min-width: 200px;
}
/* Keep CE Approved compact but without forcing space-between gaps */
.filter-row.filter-selects .filter-item.filter-ce_approved {
    flex: 0 0 120px;
    min-width: 120px;
}
.woocommerce-checkout textarea {
    width: 100% !important;
    border: 1px solid #D1D6DC !important;
}

.custom-notee {
    width: 100% !important;
}
/* .custom-notee label {
    display: flex !important;
    white-space: nowrap;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start !important;
    margin-bottom: -10px !important;
}
.custom-notee .optional {
    position: absolute;
    top: 5px;
    right: 0;
    z-index: 10;
}
.custom-notee small {
	display: block !important;
	color: #999;
	white-space: wrap;
	font-weight: 400;
	line-height: 1.6;
} */
