<?php
/**
 * My Addresses
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/my-address.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 2.6.0
 */

defined( 'ABSPATH' ) || exit;

$customer_id = get_current_user_id();

if ( ! wc_ship_to_multiple_addresses_enabled() ) {
	$get_addresses = apply_filters(
		'woocommerce_my_account_get_addresses',
		array(
			'billing'  => __( 'Billing address', 'woocommerce' ),
			'shipping' => __( 'Shipping address', 'woocommerce' ),
		),
		$customer_id
	);
} else {
	$get_addresses = apply_filters(
		'woocommerce_my_account_get_addresses',
		array(
			'billing' => __( 'Billing address', 'woocommerce' ),
		),
		$customer_id
	);
}

$oldcol = 1;
$col     = 1;
?>

<p>
	<?php echo apply_filters( 'woocommerce_my_account_my_address_description', esc_html__( 'The following addresses will be used on the checkout page by default.', 'woocommerce' ) ); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?>
</p>

<?php if ( ! wc_ship_to_multiple_addresses_enabled() ) : ?>
	<div class="u-columns woocommerce-Addresses col2-set addresses">
<?php endif; ?>

<?php foreach ( $get_addresses as $name => $address_title ) : ?>
	<?php
		$address = wc_get_account_formatted_address( $name );
		$col     = $col * -1;
		$oldcol  = $oldcol * -1;
	?>

	<?php if ( ! wc_ship_to_multiple_addresses_enabled() ) : ?>
		<div class="u-column<?php echo $col < 0 ? 1 : 2; ?> col-<?php echo $oldcol < 0 ? 1 : 2; ?> woocommerce-Address">
			<header class="woocommerce-Address-title title">
				<h2><?php echo esc_html( $address_title ); ?></h2>
				<a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', $name ) ); ?>" class="edit">
					<?php
					printf(
						/* translators: %s: Address title */
						$address ? esc_html__( 'Edit %s', 'woocommerce' ) : esc_html__( 'Add %s', 'woocommerce' ),
						esc_html( $address_title )
					);
					?>
				</a>
			</header>
			<address>
				<?php
					echo $address ? wp_kses_post( $address ) : esc_html_e( 'You have not set up this type of address yet.', 'woocommerce' );
				?>
			</address>
		</div>
	<?php endif; ?>

<?php endforeach; ?>

<?php if ( ! wc_ship_to_multiple_addresses_enabled() ) : ?>
	</div>
	<?php
		do_action( 'woocommerce_before_edit_address_form_billing' );
		do_action( 'woocommerce_before_edit_address_form_shipping' );
	?>
<?php endif; ?>

<?php
// Add SAP ShipTo addresses section
if ( function_exists( 'get_custom_user_addresses' ) ) {
	$sap_addresses = get_custom_user_addresses( get_current_user_id() );
	
	if ( is_array( $sap_addresses ) && ! empty( $sap_addresses ) ) {
		// Filter for shipping addresses only
		$shipping_addresses = array_filter( $sap_addresses, function( $address ) {
			return isset( $address['type'] ) && $address['type'] === 'shipping';
		});
		
		if ( ! empty( $shipping_addresses ) ) {
			?>
			<div class="sap-shipto-addresses-section" style="margin-top: 40px;">
				<h2 style="margin-bottom: 20px;">
					Ship To Addresses 
					<span style="color: #0073aa; font-size: 14px; font-weight: normal;">(SAP ShipTo)</span>
				</h2>
				
				<div class="sap-shipto-addresses-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
					<?php foreach ( $shipping_addresses as $address ): ?>
						<div class="sap-shipto-address-card" style="border: 1px solid #ddd; padding: 20px; border-radius: 5px; background: #f9f9f9;">
							<div class="address-header" style="margin-bottom: 15px;">
								<h3 style="margin: 0 0 5px 0; color: #333;">
									<?php echo esc_html( $address['address_internal_name'] ?? 'Unnamed Address' ); ?>
								</h3>
								
								<?php if ( isset( $address['shipping_is_default_address'] ) && $address['shipping_is_default_address'] ): ?>
									<span class="default-badge" style="background: #0073aa; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px;">
										Default
									</span>
								<?php endif; ?>
								
								<div style="font-size: 12px; color: #666; margin-top: 5px;">
									Address ID: <?php echo esc_html( $address['address_id'] ?? 'N/A' ); ?>
								</div>
							</div>
							
							<div class="address-details" style="line-height: 1.5;">
								<?php if ( ! empty( $address['shipping_company'] ) ): ?>
									<div style="font-weight: bold; margin-bottom: 5px;">
										<?php echo esc_html( $address['shipping_company'] ); ?>
									</div>
								<?php endif; ?>
								
								<?php if ( ! empty( $address['shipping_address_1'] ) ): ?>
									<div><?php echo esc_html( $address['shipping_address_1'] ); ?></div>
								<?php endif; ?>
								
								<?php if ( ! empty( $address['shipping_address_2'] ) ): ?>
									<div><?php echo esc_html( $address['shipping_address_2'] ); ?></div>
								<?php endif; ?>
								
								<div>
									<?php echo esc_html( $address['shipping_city'] ?? '' ); ?>
									<?php if ( ! empty( $address['shipping_state'] ) ): ?>
										, <?php echo esc_html( $address['shipping_state'] ); ?>
									<?php endif; ?>
									<?php echo esc_html( $address['shipping_postcode'] ?? '' ); ?>
								</div>
								
								<?php if ( ! empty( $address['shipping_country'] ) ): ?>
									<div style="margin-top: 5px;">
										<?php echo esc_html( WC()->countries->countries[ $address['shipping_country'] ] ?? $address['shipping_country'] ); ?>
									</div>
								<?php endif; ?>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
				
				<div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-left: 4px solid #0073aa; font-size: 14px;">
					<strong>Note:</strong> These addresses are managed through your SAP system and are automatically synchronized.
				</div>
			</div>
			
			<style>
				@media (max-width: 768px) {
					.sap-shipto-addresses-grid {
						grid-template-columns: 1fr !important;
					}
				}
			</style>
			<?php
		}
	}
}
?>

<style>
    #wcmca_add_new_address_button_billing {
        display: none !important;
    }
</style>
