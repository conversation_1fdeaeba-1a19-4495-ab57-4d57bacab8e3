<?php
function price_group_code_eur_page() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'price_code_group_relation_eur';

    handle_price_group_code_relation_eur($table_name);
}

function handle_price_group_code_relation_eur($table_name) {
    global $wpdb;

    $price_codes = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}price_code_list_eur");
    $price_groups = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}price_group_eur");

    if (isset($_POST['update_relation'])) {
        foreach ($_POST['relation'] as $group_id => $codes) {
            foreach ($codes as $code_id => $status) {
                $wpdb->update(
                    $table_name,
                    array('status' => sanitize_text_field($status)),
                    array('price_code_id' => intval($code_id), 'price_group_id' => intval($group_id))
                );
            }
        }
    }

    $relations = $wpdb->get_results("SELECT * FROM $table_name", OBJECT_K);
    $relation_matrix = array();
    foreach ($relations as $relation) {
        $relation_matrix[$relation->price_group_id][$relation->price_code_id] = $relation->status;
    }

    ?>
    <div class="wrap">
        <h1>Manage Price Group & Code Relations</h1>
        <form method="post">
            <table class="widefat fixed" cellspacing="0">
                <thead>
                    <tr>
                        <th>Price Group</th>
                        <?php foreach ($price_codes as $code): ?>
                            <th><?php echo esc_html($code->price_code_title); ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($price_groups as $group): ?>
                        <tr>
                            <td><?php echo esc_html($group->price_group_title); ?></td>
                            <?php foreach ($price_codes as $code): ?>
                                <td>
                                    <select name="relation[<?php echo intval($group->id); ?>][<?php echo intval($code->id); ?>]">
                                        <option value="No" <?php selected($relation_matrix[$group->id][$code->id], 'No'); ?>>No</option>
                                        <option value="Yes" <?php selected($relation_matrix[$group->id][$code->id], 'Yes'); ?>>Yes</option>
                                    </select>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <p class="submit">
                <input type="submit" name="update_relation" class="button button-primary" value="Update Relations">
            </p>
        </form>
    </div>
    <?php
}