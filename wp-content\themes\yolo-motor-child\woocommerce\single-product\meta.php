<?php
/**
 * Single Product Meta
 *
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     3.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

global $product;

$weight = $product->get_weight();

$availability      = $product->get_availability();
if (empty($availability['availability'])) {
    $availability['availability'] = esc_html__( 'In stock', 'yolo-motor' );
    $availability['class'] = 'in-stock';
}
$availability_html = '<span class="product-stock-status stock ' . esc_attr( $availability['class'] ) . '">' . esc_html( $availability['availability'] ) . '</span>';

// Get customer location for stock determination
$main_user_id = get_current_user_id();
$company_code = get_user_meta($main_user_id, '_companycode', true);
$is_us_customer = ($company_code == "3090");

// Get live inventory based on customer location
$stock_meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';
$live_stock = get_post_meta( get_the_ID(), $stock_meta_key, true );
$live_stock = intval($live_stock); // Convert to integer

?>
<div class="product_meta">

    <?php do_action( 'woocommerce_product_meta_start' ); ?>

    <?php
    // Brand field - only show if brand exists
    $terms = get_the_terms( $product->get_id(), 'product_cat' );
    $brand = get_post_meta( get_the_ID(), '_brand', true );
    $has_brand = (!empty($terms) && !is_wp_error($terms)) || !empty($brand);

    if ($has_brand) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Brand:', 'yolo-motor' ); ?></label>
        <?php
            if (!empty($terms) && ! is_wp_error($terms)) {
                foreach($terms as $term ) {
                    $thumbnail_id = get_term_meta( $term->term_id, 'thumbnail_id', true);
                    if($thumbnail_id) {
                        $image_url = wp_get_attachment_url($thumbnail_id);
                        echo '<span class="sku" itemprop="brand"><img src="' . esc_url($image_url) . '" alt="' . esc_attr($term->name) . '"></span>';
                    } else {
                        echo '<span class="sku" itemprop="brand">' . esc_html($term->name) . '</span>';
                    }
                    break;
                }
            } else {
                echo '<span class="sku" itemprop="brand">' . esc_html( $brand ) . '</span>';
            }
        ?>
    </span>
    <?php endif; ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Material Number:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="brand"><?php the_title(); ?></span>
    </span>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Material Description:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="description">
            <?php
                $description = get_post_field( 'post_content', $product->get_id() );
                echo wp_trim_words( $description, 10, '...' );
            ?>
        </span>
    </span>
    <?php
    // Price Code - only show if exists
    $price_code = get_post_meta( get_the_ID(), '_brand', true );
    if (!empty($price_code)) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Price Code:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="brand">
            <span class="sku" itemprop="brand"><?php echo esc_html($price_code); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // Weight - only show if weight exists
    if (!empty($weight)) :
        $main_user_id = get_current_user_id();
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        $is_us_customer = ($company_code == "3090");

        $display_weight = $is_us_customer ? $weight : round($weight * 0.453592, 2);
        $weight_unit = $is_us_customer ? 'lbs' : 'kg';
    ?>
    <span class="sku_wrapper weight_wrapper">
        <label><?php esc_html_e('Weight:', 'yolo-motor'); ?></label>
        <span class="weight" itemprop="weight">
            <span class="weight_value"><?php echo esc_html($display_weight) . ' ' . $weight_unit; ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // Product Line - only show if exists
    $product_line = get_post_meta( get_the_ID(), '_product_line', true );
    if (!empty($product_line)) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Product Line:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="_product_line">
            <span class="sku" itemprop="_product_line"><?php echo esc_html($product_line); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // Product Family - only show if exists
    $product_family = get_post_meta( get_the_ID(), '_product_family', true );
    if (!empty($product_family)) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Product Family:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="_product_family">
            <span class="sku" itemprop="_product_family"><?php echo esc_html($product_family); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // Product Series - only show if exists
    $product_series = get_post_meta( get_the_ID(), '_product_series', true );
    if (!empty($product_series)) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Product Series:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="_product_series">
            <span class="sku" itemprop="_product_series"><?php echo esc_html($product_series); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // Model/Size - only show if exists
    $model_size = get_post_meta( get_the_ID(), '_model_size', true );
    if (!empty($model_size)) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Model/Size:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="_model_size">
            <span class="sku" itemprop="_model_size"><?php echo esc_html($model_size); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // CE Approved - only show if exists
    $ce_approved = get_post_meta( get_the_ID(), '_ce_approved', true );
    if (!empty($ce_approved)) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'CE Approved:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="_ce_approved">
            <span class="sku" itemprop="_ce_approved"><?php echo esc_html($ce_approved); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <?php
    // Stock - only show if stock value exists
    $main_user_id = get_current_user_id();
    $company_code = get_user_meta($main_user_id, '_companycode', true);
    $is_us_customer = ($company_code == "3090");
    $stock_meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';
    $live_stock = get_post_meta(get_the_ID(), $stock_meta_key, true);

    if ($live_stock !== '' && $live_stock !== null) : ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'Stock:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="_stock_approved">
            <span class="sku" itemprop="_stock_approved"><?php echo max(0, intval($live_stock)); ?></span>
        </span>
    </span>
    <?php endif; ?>
    <span class="sku_wrapper">
        <label><?php esc_html_e( 'List price:', 'yolo-motor' ); ?></label>
        <span class="sku" itemprop="brand">
            <span class="sku" itemprop="price"><?php echo $product->get_price_html(); ?></span>
        </span>
    </span>

    <?php
    // Inject SAP Net Price and Sales Tax under List Price (same style)
    if ( function_exists('hytec_sap_get_or_fetch_pricing') && is_user_logged_in() ) {
        $pricing = hytec_sap_get_or_fetch_pricing( $product, get_current_user_id() );
        if ( is_array( $pricing ) ) {
            $net_value = isset($pricing['net_value']) ? $pricing['net_value'] : '';
            $sales_tax = isset($pricing['sales_tax']) ? $pricing['sales_tax'] : '';

            // Determine currency symbol based on user meta
            $main_user_id  = get_current_user_id();
            $company_code  = get_user_meta($main_user_id, '_companycode', true);
            $country_code  = get_user_meta($main_user_id, '_country', true);
            if ($company_code === '3090') {
                $currency_symbol = '$';
            } else {
                $currency_symbol = ($country_code === 'GB') ? '£' : '€';
            }
            ?>
            <span class="sku_wrapper sap-row sap-net-row">
                <label><?php esc_html_e( 'Net Price:', 'yolo-motor' ); ?></label> 
                <span class="sku" itemprop="net_price">
                    <span class="sku" itemprop="net_price"><?php
                        // Convert SAP response (divide by 100 for proper decimal format)
                        $formatted_net_value = is_numeric( $net_value ) ? number_format( (float)$net_value / 100, 2 ) : $net_value;
                        echo esc_html( $currency_symbol . $formatted_net_value );
                    ?></span>
                </span>
            </span>
            <span class="sku_wrapper sap-row sap-tax-row">
                <label><?php esc_html_e( 'Sales Tax:', 'yolo-motor' ); ?></label>
                <span class="sku" itemprop="sales_tax">
                    <span class="sku" itemprop="sales_tax"><?php
                        // Convert SAP response (divide by 100 for proper decimal format)
                        $formatted_sales_tax = is_numeric( $sales_tax ) ? number_format( (float)$sales_tax / 100, 2 ) : $sales_tax;
                        echo esc_html( $currency_symbol . $formatted_sales_tax );
                    ?></span>
                </span>
            </span>
            <?php
        }
    }
    ?>


<?php do_action( 'woocommerce_product_meta_end' ); ?>
</div> 