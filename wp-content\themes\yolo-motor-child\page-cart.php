<?php
/**
 * Custom Cart Page Template (No WooCommerce Blocks)
 * Template Name: Custom Cart
 */

get_header(); ?>
<style>
.page-title {
	font-family: 'HelveticaNeue', Arial, helvetica, sans-serif !important;
	text-transform: none;
	font-weight: 400 !important;
	color: #000 !important;
	font-size: 44px !important;
}
.woocommerce-checkout .content-page-title, .woocommerce-cart .content-page-title {
	border-bottom: 1px solid #D1D6DC;
}
.woocommerce-checkout .content-page-title .page-title, .woocommerce-cart .content-page-title .page-title {
	padding-top: 40px;
	padding-bottom: 40px;
}
.woocommerce-cart .content-page-title .page-title {
	display: flex;
	align-items: first baseline;
}
.woocommerce table.shop_table.cart .product-subtotal {
	border-right: none;
    padding-right: 50px !important;
    position: relative;
}
.woocommerce table.shop_table.cart .product-remove {
	border-left: none;
	text-align: center;
}
.product-name .woocommerce-Price-amount.amount bdi {
	font-size: 18px;
	justify-content: flex-start;
}
.remove_item_link {
	position: absolute;
	right: 0;
	top: calc(50% - 8px);
	font-size: 0 !important;
	color: red !important;
	text-decoration: none !important;
	background: url(/wp-content/themes/yolo-motor-child/assets/images/trash.svg) no-repeat center center !important;
	width: 14px !important;
	height: 16px !important;
	border-radius: 0 !important;
}
.woocommerce table.shop_table.cart thead th {
	background: none !important;
	color: #fff;
	font-size: 13px;
	text-transform: uppercase;
	font-weight: bold;
	padding: 20px 0;
	font-family: "HelveticaNeue";
	font-size: 18px;
	font-weight: 400 !important;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: #757575;
	text-transform: capitalize;
}
.cart-collaterals h2 {
	border-left: none;
	padding-left: 0;
	display: none;
}
.woocommerce .cart-collaterals .cart_totals {
	width: 100%;
	background: #EDF1F2;
	padding: 20px 20px 20px;
	font-size: 16px !important;
}
body.woocommerce-cart .wc-proceed-to-checkout a.checkout-button:hover {
    background-color: #c44225 !important;
}
body.woocommerce-cart .wc-proceed-to-checkout a.checkout-button {
	background-color: #EB5534 !important;
	border-radius: 0;
	font-size: 16px;
    margin-bottom: 0 !important;
}
.cart-subtotal {
	display: block;
}
.woocommerce table.shop_table {
	border: none !important;
}
.woocommerce-cart .cart-collaterals .cart_totals table th, .woocommerce-cart .cart-collaterals .cart_totals table td {
	line-height: 1.8;
	padding-left: 0;
	padding-right: 0;
}
.cart-subtotal2 th, .cart-subtotal2 td {
	border-top: none !important;
    font-size: 20px;
}
.wc-cart-item__image img {
    width: 100% !important;
    height: auto !important;
}
.wc-cart-item__image a {
	min-height: 87px;
	display: block;
	width: 100px;
}
.wc-cart-item__image {
	display: flex;
	border: 1px solid #dadada;
	max-width: 110px !important;
	overflow: hidden;
	align-items: center;
	justify-content: center;
}
.woocommerce-cart table.cart .product-thumbnail {
	max-width: 60px;
}
body.woocommerce-cart .wc-proceed-to-checkout, 
body.woocommerce-checkout .wc-proceed-to-checkout {
	padding: 1em 0 0;
}
.woocommerce table.shop_table.cart tbody .product-name {
	position: relative;
	overflow: hidden;
	font-weight: 700;
	font-size: 16px;
}
.woocommerce table.shop_table.cart thead th.product-thumbnail {
	white-space: nowrap;
}
@media screen and (max-width: 991px) {
.woocommerce-cart .woocommerce-privacy-policy-text {
    transform: none !important;
}
.woocommerce table.shop_table_responsive tr, .woocommerce-page table.shop_table_responsive tr {
	display: flex;
}
.cart-subtotal2 th {
	display: flex !important;
}
.cart-subtotal2 th, .cart-subtotal2 td {
	border-top: none !important;
	font-size: 15px;
}
.woocommerce table.shop_table_responsive tr td::before, 
.woocommerce-page table.shop_table_responsive tr td::before {
	display: none !important;
}
}
</style>
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="content-page-title">
                <h1 class="page-title">
                    Cart <span class="green">5 items</span>
                    <a href="javascript:;" class="empty-cart-link" id="empty-cart-button" style="margin-left: 15px;">Empty Cart</a>
                </h1>
            </div>
        </div>
            
        <?php
        // Check if WooCommerce is active and cart has items
        if ( class_exists( 'WooCommerce' ) ) {
            
            if ( WC()->cart->is_empty() ) {
                ?>
                <div class="wc-empty-cart-message">
                    <p><?php esc_html_e( 'Your cart is currently empty.', 'woocommerce' ); ?></p>
                    <p>
                        <a class="button wc-backward" href="<?php echo esc_url( apply_filters( 'woocommerce_return_to_shop_redirect', wc_get_page_permalink( 'shop' ) ) ); ?>">
                            <?php esc_html_e( 'Return to shop', 'woocommerce' ); ?>
                        </a>
                    </p>
                </div>
                <?php
            } else {
        ?>
            <div class="col-md-8 mt-4">
                <form class="woocommerce-cart-form" action="<?php echo esc_url( wc_get_cart_url() ); ?>" method="post">
                    <?php do_action( 'woocommerce_before_cart_table' ); ?>

                    <table class="shop_table shop_table_responsive cart woocommerce-cart-form__contents" cellspacing="0">
                        <thead>
                            <tr>
                                <!-- <th class="product-remove">&nbsp;</th> -->
                                <th class="product-thumbnail"><?php esc_html_e( 'Product Details', 'woocommerce' ); ?></th>
                                <th class="product-name"></th>
                                <!-- <th class="product-price"><?php // esc_html_e( 'Price', 'woocommerce' ); ?></th> -->
                                <th class="product-quantity"><?php esc_html_e( 'Quantity', 'woocommerce' ); ?></th>
                                <th class="product-subtotal" style="text-align: right"><?php esc_html_e( 'Total', 'woocommerce' ); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php do_action( 'woocommerce_before_cart_contents' ); ?>

                            <?php
                            foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
                                $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
                                $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

                                if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
                                    $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
                                    ?>
                                    <tr class="woocommerce-cart-form__cart-item <?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>">

                                        <td class="product-thumbnail">
                                            <div class="wc-cart-item__image">
                                            <?php
                                            // $nnn = $_product->get_image();
                                            // echo $nnn;
                                            $thumbnail = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image(), $cart_item, $cart_item_key );
                                    
                                            if ( ! $product_permalink ) {
                                                echo $thumbnail; // PHPCS: XSS ok.
                                            } else {
                                                printf( '<a href="%s">%s</a>', esc_url( $product_permalink ), $thumbnail ); // PHPCS: XSS ok.
                                            }
                                            ?>
                                            </div>
                                        </td>

                                        <td class="product-name" data-title="<?php esc_attr_e( 'Product', 'woocommerce' ); ?>">
                                            <?php
                                            if ( ! $product_permalink ) {
                                                echo wp_kses_post( apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) . '&nbsp;' );
                                            } else {
                                                echo wp_kses_post( apply_filters( 'woocommerce_cart_item_name', sprintf( '<a href="%s">%s</a>', esc_url( $product_permalink ), $_product->get_name() ), $cart_item, $cart_item_key ) );
                                            }

                                            do_action( 'woocommerce_after_cart_item_name', $cart_item, $cart_item_key );

                                            // Meta data
                                            // echo wc_get_formatted_cart_item_data( $cart_item ); // PHPCS: XSS ok.

                                            // get cart item description 
                                            $cart_item_description = $cart_item['data']->get_description();
                                            echo '<p class="mb-0">' . $cart_item_description . '</p>';
                                            // Backorder notification
                                            if ( $_product->backorders_require_notification() && $_product->is_on_backorder( $cart_item['quantity'] ) ) {
                                                echo wp_kses_post( apply_filters( 'woocommerce_cart_item_backorder_notification', '<p class="backorder_notification">' . esc_html__( 'Available on backorder', 'woocommerce' ) . '</p>', $product_id ) );
                                            }
                                            ?>
                                            <?php
                                            echo '<div class="flex aic">LIST PRICE: &nbsp;' . apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key ) . '</div>'; // PHPCS: XSS ok.
                                            ?>

                                        </td>

                                        <td class="product-quantity" data-title="<?php esc_attr_e( 'Quantity', 'woocommerce' ); ?>">
                                            <?php
                                            if ( $_product->is_sold_individually() ) {
                                                $min_quantity = 1;
                                                $max_quantity = 1;
                                            } else {
                                                $min_quantity = 0;
                                                $max_quantity = $_product->get_max_purchase_quantity();
                                            }

                                            // $product_quantity = woocommerce_quantity_input(
                                            //     array(
                                            //         'input_name'   => "cart[{$cart_item_key}][qty]",
                                            //         'input_value'  => $cart_item['quantity'],
                                            //         'max_value'    => $max_quantity,
                                            //         'min_value'    => $min_quantity,
                                            //         'product_name' => $_product->get_name(),
                                            //     ),
                                            //     $_product,
                                            //     false
                                            // );

                                            // echo apply_filters( 'woocommerce_cart_item_quantity', $product_quantity, $cart_item_key, $cart_item ); // PHPCS: XSS ok.
                                            ?>
                                            <div class="wc-block-components-quantity-selector">
                                                <input class="wc-block-components-quantity-selector__input qty_input" type="number" step="1" min="<?php echo $min_quantity; ?>" max="<?php echo $max_quantity < 0 ? '' : $max_quantity; ?>" aria-label="<?php echo $_product->get_name(); ?> quantity" value="<?php echo $cart_item['quantity']; ?>" name="<?php echo "cart[{$cart_item_key}][qty]"; ?>">
                                                <button type="button" aria-label="Reduce quantity of 100860" class="wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--minus">－</button>
                                                <button type="button" aria-label="Increase quantity of 100860" class="wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--plus">＋</button>
                                            </div>
                                        </td>

                                        <td class="product-subtotal" data-title="<?php esc_attr_e( 'Subtotal', 'woocommerce' ); ?>">
                                            <?php
                                            echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); // PHPCS: XSS ok.
                                            ?>
                                            <?php
                                            echo apply_filters( // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
                                                'woocommerce_cart_item_remove_link',
                                                sprintf(
                                                    '<a href="%s" class="remove remove_item_link" aria-label="%s" data-product_id="%s" data-product_sku="%s">&times;</a>',
                                                    esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
                                                    esc_html__( 'Remove this item', 'woocommerce' ),
                                                    esc_attr( $product_id ),
                                                    esc_attr( $_product->get_sku() )
                                                ),
                                                $cart_item_key
                                            );
                                            ?>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }
                            ?>

                            <?php do_action( 'woocommerce_cart_contents' ); ?>

                            <tr>
                                <td colspan="6" class="actions">
                                    <?php if ( wc_coupons_enabled() ) { ?>
                                        <div class="coupon">
                                            <label for="coupon_code"><?php esc_html_e( 'Coupon:', 'woocommerce' ); ?></label> 
                                            <input type="text" name="coupon_code" class="input-text" id="coupon_code" value="" placeholder="<?php esc_attr_e( 'Coupon code', 'woocommerce' ); ?>" /> 
                                            <button type="submit" class="button" name="apply_coupon" value="<?php esc_attr_e( 'Apply coupon', 'woocommerce' ); ?>"><?php esc_attr_e( 'Apply coupon', 'woocommerce' ); ?></button>
                                            <?php do_action( 'woocommerce_cart_coupon' ); ?>
                                        </div>
                                    <?php } ?>

                                    <button type="submit" class="button" name="update_cart" value="<?php esc_attr_e( 'Update cart', 'woocommerce' ); ?>"><?php esc_html_e( 'Update cart', 'woocommerce' ); ?></button>

                                    <?php do_action( 'woocommerce_cart_actions' ); ?>

                                    <?php wp_nonce_field( 'woocommerce-cart', 'woocommerce-cart-nonce' ); ?>
                                </td>
                            </tr>

                            <?php do_action( 'woocommerce_after_cart_contents' ); ?>
                        </tbody>
                    </table>
                    <?php // do_action( 'woocommerce_after_cart_table' ); ?>
                </form>

                <?php do_action( 'woocommerce_before_cart_collaterals' ); ?>
            </div>
            <div class="col-md-4 mt-4">
                <div class="cart-collaterals">
                    <?php
                    /**
                     * Cart collaterals hook.
                     *
                     * @hooked woocommerce_cross_sell_display
                     * @hooked woocommerce_cart_totals - 10
                     */
                    do_action( 'woocommerce_cart_collaterals' );
                    ?>
                </div>

                <?php do_action( 'woocommerce_after_cart' ); ?>
            </div>
            <?php
            }
        } else {
            echo '<div class="col-md-12"><p>WooCommerce is not active.</p></div>';
        }
        ?>
    </div>
</div>
<script>
jQuery(document).ready(function($) {
    jQuery(document).on('click', '.plus, .wc-block-components-quantity-selector__button--plus', function(e) {
        var $input = jQuery(this).siblings('input.qty_input');
        var value = parseInt($input.val());
        jQuery('button.actions').prop('disabled', false);
        jQuery('button[name="update_cart"]').prop('disabled', false);
        $input.val(value + 1);
    });

    jQuery(document).on('click', '.minus, .wc-block-components-quantity-selector__button--minus', function(e) {
        var $input = jQuery(this).siblings('input.qty_input');
        var value = parseInt($input.val());
        if (value > 1) {
            $input.val(value - 1);                
        }else{
            $input.val(1);
        }
        jQuery('button.actions').prop('disabled', false);
        jQuery('button[name="update_cart"]').prop('disabled', false);

    });
});
</script>
<?php get_footer(); ?>
