<?php
/**
 * SAP SalesOrderS pricing integration for single product pages
 * - Builds payload from current product and current user (Sold-To)
 * - Calls external endpoint and renders Net Price and Sales Tax
 */

if ( ! defined( 'ABSPATH' ) ) { exit; }

// Console logging helper (no secrets)
if ( ! function_exists( 'hytec_sap_pricing_console_enabled' ) ) {
    function hytec_sap_pricing_console_enabled() {
        $enabled = true; // default on for rollout
        if ( defined( 'HYTEC_SAP_PRICING_CONSOLE' ) ) {
            $enabled = (bool) HYTEC_SAP_PRICING_CONSOLE;
        }
        return (bool) apply_filters( 'hytec_sap_pricing_console_enabled', $enabled );
    }
}

if ( ! function_exists( 'hytec_sap_debug_ui_enabled' ) ) {
    function hytec_sap_debug_ui_enabled() {
        $enabled = true; // default on for rollout
        if ( defined( 'HYTEC_SAP_DEBUG_UI' ) ) {
            $enabled = (bool) HYTEC_SAP_DEBUG_UI;
        }
        return (bool) apply_filters( 'hytec_sap_debug_ui_enabled', $enabled );
    }
}

if ( ! function_exists( 'hytec_sap_console_log' ) ) {
    function hytec_sap_console_log( $level, $message, $context = array() ) {
        if ( ! hytec_sap_pricing_console_enabled() ) return;
        if ( ! in_array( $level, array( 'log', 'info', 'warn', 'error' ), true ) ) {
            $level = 'log';
        }
        foreach ( array( 'Authorization','authorization','token','auth' ) as $k ) {
            if ( isset( $context[ $k ] ) ) unset( $context[ $k ] );
        }
        $data = array( 'msg' => (string) $message, 'ctx' => $context );
        $json = wp_json_encode( $data );
        echo "<script>(function(){try{console[$level]($json);}catch(e){}})();</script>";
    }
}

// Env/constant configuration
if ( ! function_exists( 'hytec_sap_api_get_url' ) ) {
    function hytec_sap_api_get_url() {
        $val = getenv('HYTEC_SAP_SALES_ORDER_URL');
        if ( empty( $val ) && defined( 'HYTEC_SAP_SALES_ORDER_URL' ) ) {
            $val = HYTEC_SAP_SALES_ORDER_URL;
        }
        if ( $val && filter_var( $val, FILTER_VALIDATE_URL ) ) {
            return $val;
        }
        $default = 'https://hytec-dev-8ouoom67.it-cpi034-rt.cfapps.us10-002.hana.ondemand.com/http/WC/SalesOrderS';
        return apply_filters( 'hytec_sap_sales_order_url', $default );
    }
}

if ( ! function_exists( 'hytec_sap_api_get_auth_header' ) ) {
    function hytec_sap_api_get_auth_header() {
        $token = getenv('HYTEC_SAP_API_TOKEN');
        if ( empty( $token ) && defined( 'HYTEC_SAP_API_TOKEN' ) ) {
            $token = HYTEC_SAP_API_TOKEN;
        }
        if ( empty( $token ) ) {
            return '';
        }
        $has_scheme = preg_match('/^(Bearer|Basic)\s+/i', $token) === 1;
        return apply_filters( 'hytec_sap_api_auth_header', $has_scheme ? $token : ('Bearer ' . $token) );
    }
}

// Formatting helper: pad numeric SKU to 18 chars
if ( ! function_exists( 'hytec_format_material_id' ) ) {
    function hytec_format_material_id( $sku ) {
        $sku = trim( (string) $sku );
        if ( $sku === '' ) return '';
        if ( ctype_digit( $sku ) ) {
            return strlen( $sku ) < 18 ? str_pad( $sku, 18, '0', STR_PAD_LEFT ) : $sku;
        }
        return $sku;
    }
}

// Sold-To lookup from custom table
if ( ! function_exists( 'hytec_get_sap_customer_for_user' ) ) {
    function hytec_get_sap_customer_for_user( $user_id ) {
        if ( function_exists( 'sap_cart_get_customer_data' ) ) {
            $row = sap_cart_get_customer_data( $user_id );
            if ( $row ) return $row;
        }
        global $wpdb;
        $customer_id = get_user_meta( $user_id, '_customer', true );
        if ( empty( $customer_id ) ) return null;
        $table_name = $wpdb->prefix . 'sap_soldto_customers';
        if ( $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) ) !== $table_name ) return null;
        $row = $wpdb->get_row( $wpdb->prepare(
            "SELECT customer_id, company_code, country_code, price_group, Z7_Partner_no, company, address_line1, address_line2, city, postcode, country_region, state_county FROM {$table_name} WHERE customer_id = %s",
            $customer_id
        ) );
        return $row ?: null;
    }
}

// Build payload
if ( ! function_exists( 'hytec_build_sales_order_payload' ) ) {
    function hytec_build_sales_order_payload( $product, $user_id ) {
        $sku = $product instanceof WC_Product ? $product->get_sku() : '';
        $material_id = hytec_format_material_id( $sku );

        if ( $material_id === '' ) {
            hytec_sap_console_log('warn', 'Empty material_id (SKU missing on product)');
        }

        $user = get_userdata( $user_id );
        $first_name = get_user_meta( $user_id, 'first_name', true );
        $last_name  = get_user_meta( $user_id, 'last_name', true );
        $email      = $user ? $user->user_email : '';

        $customer_meta = get_user_meta( $user_id, '_customer', true );
        $sap_customer = hytec_get_sap_customer_for_user( $user_id );

        hytec_sap_console_log('log', 'SAP customer lookup', array(
            '_customer_meta' => $customer_meta,
            'found' => (bool) $sap_customer,
        ));

        if ( ! $sap_customer ) return null;

        $billing = array(
            'billing_id'    => (string) ( $sap_customer->customer_id ?? '' ),
            'Z7_Partner_no' => (string) ( $sap_customer->Z7_Partner_no ?? '' ),
            'first_name'    => (string) $first_name,
            'last_name'     => (string) $last_name,
            'company_code'  => (string) ( $sap_customer->company_code ?? '' ),
            'company_name'  => (string) ( $sap_customer->company ?? '' ),
            'address_1'     => (string) ( $sap_customer->address_line1 ?? '' ),
            'address_2'     => (string) ( $sap_customer->address_line2 ?? '' ),
            'city'          => (string) ( $sap_customer->city ?? '' ),
            'state'         => (string) ( $sap_customer->state_county ?? '' ),
            'postcode'      => (string) ( $sap_customer->postcode ?? '' ),
            'country'       => (string) ( $sap_customer->country_code ?? $sap_customer->country_region ?? '' ),
            'email'         => (string) $email,
        );

        $currency = function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'USD';
        $timestamp = current_time( 'timestamp' );
        $date_iso  = date_i18n( 'Y-m-d\TH:i:s', $timestamp );

        // Format quantity: multiply by 1000 for whole numbers, keep decimal for fractional
        $quantity = 1; // Default quantity for pricing lookup
        $formatted_quantity = $quantity;

        // If it's a whole number, multiply by 1000
        if (floor($quantity) == $quantity) {
            $formatted_quantity = $quantity * 1000;
        } else {
            // For fractional quantities, format to 4 decimal places
            $formatted_quantity = number_format($quantity, 4, '.', '');
        }

        // Debug log the quantity formatting
        error_log("SAP Quantity formatting: original=$quantity, formatted=$formatted_quantity");

        $payload = array(
            'order' => array(
                'date_created' => $date_iso,
                'currency'     => $currency,
                'billing'      => $billing,
                'line_items'   => array(
                    array(
                        'product_id' => $material_id,
                        'quantity'   => 1000, // Force 1000 for testing
                    )
                ),
            ),
        );

        // Log user context and billing info
        hytec_sap_console_log('log', 'Current user context', array(
            'user_id'    => $user_id,
            'email'      => $email,
            'first_name' => $first_name,
            'last_name'  => $last_name,
            'billing'    => $billing,
        ));

        return $payload;
    }
}

// Call endpoint
if ( ! function_exists( 'hytec_call_sap_sales_order' ) ) {
    function hytec_call_sap_sales_order( $payload ) {
        $url = hytec_sap_api_get_url();
        $auth = hytec_sap_api_get_auth_header();
        if ( empty( $url ) || empty( $auth ) ) {
            error_log( 'SAP Product Pricing: Missing URL or Authorization token.' );
            return new WP_Error( 'sap_config', 'Missing SAP configuration.' );
        }
        $args = array(
            'method'      => 'POST',
            'timeout'     => 10,
            'headers'     => array(
                'Content-Type'  => 'application/json',
                'Authorization' => $auth,
            ),
            'body'        => wp_json_encode( $payload ),
            'data_format' => 'body',
        );
        $response = wp_remote_post( $url, $args );
        if ( is_wp_error( $response ) ) return $response;
        $code = wp_remote_retrieve_response_code( $response );
        $body = wp_remote_retrieve_body( $response );
        if ( $code < 200 || $code >= 300 ) {
            return new WP_Error( 'sap_http', 'Unexpected response code: ' . $code, array( 'body' => $body ) );
        }
        $json = json_decode( $body, true );
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            return new WP_Error( 'sap_json', 'Invalid JSON response.' );
        }
        return $json;
    }
}

// Render section
if ( ! function_exists( 'hytec_render_pricing_section' ) ) {
    function hytec_render_pricing_section( $net_value, $sales_tax, $note = '' ) {
        echo '<section class="sap-pricing" style="margin-top:15px;">';
        echo '<h3 class="sap-pricing-title" style="font-size:1rem;margin-bottom:8px;">Pricing</h3>';
        echo '<table class="shop_attributes" role="presentation" style="width:auto;">';
        echo '<tbody>';
        echo '<tr><th>Net Price</th><td>' . esc_html( (string) $net_value ) . '</td></tr>';
        echo '<tr><th>Sales Tax</th><td>' . esc_html( (string) $sales_tax ) . '</td></tr>';
        echo '</tbody>';
        echo '</table>';
        if ( $note !== '' && hytec_sap_debug_ui_enabled() ) {
            echo '<div class="sap-pricing-note" style="margin-top:6px;color:#888;font-size:12px;">' . esc_html( $note ) . '</div>';
        }
        echo '</section>';
    }
}

// Hook into single product summary
if ( ! function_exists( 'hytec_render_sap_pricing_block' ) ) {
    function hytec_render_sap_pricing_block() {
        if ( ! function_exists('is_product') || ! is_product() ) return;
        global $product;
        if ( ! $product instanceof WC_Product ) return;

        if ( ! is_user_logged_in() ) {
            if ( hytec_sap_debug_ui_enabled() ) hytec_render_pricing_section( '—', '—', 'Login required for SAP pricing');
            return;
        }

        $user_id = get_current_user_id();
        $payload = hytec_build_sales_order_payload( $product, $user_id );
        if ( ! $payload ) {
            if ( hytec_sap_debug_ui_enabled() ) hytec_render_pricing_section( '—', '—', 'Customer mapping not found');
            return;
        }
        $response = hytec_call_sap_sales_order( $payload );
        if ( is_wp_error( $response ) ) {
            if ( hytec_sap_debug_ui_enabled() ) hytec_render_pricing_section( '—', '—', 'SAP pricing unavailable');
            return;
        }
        $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
        $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
        $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
        $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
        hytec_render_pricing_section( $net_value, $sales_tax );
    }
    add_action( 'woocommerce_single_product_summary', 'hytec_render_sap_pricing_block', 29 );
}

// Bulk SAP pricing AJAX endpoint for table view
add_action('wp_ajax_get_sap_pricing_bulk', 'get_sap_pricing_bulk_ajax');
add_action('wp_ajax_nopriv_get_sap_pricing_bulk', 'get_sap_pricing_bulk_ajax');

function get_sap_pricing_bulk_ajax() {
    // Clean output buffer to prevent HTML from interfering with JSON
    if (ob_get_level()) {
        ob_clean();
    }

    // Disable console logging during AJAX to prevent script tags in JSON response
    add_filter('hytec_sap_pricing_console_enabled', '__return_false');

    // Add error logging
    error_log('SAP Bulk Pricing: Function called');

    if ( ! isset( $_POST['skus'] ) || ! is_array( $_POST['skus'] ) ) {
        error_log('SAP Bulk Pricing: Invalid SKUs provided');
        wp_send_json_error( 'Invalid SKUs provided' );
        return;
    }

    $skus = array_map( 'sanitize_text_field', $_POST['skus'] );
    $user_id = get_current_user_id();

    error_log('SAP Bulk Pricing: Processing SKUs: ' . print_r($skus, true));
    error_log('SAP Bulk Pricing: User ID: ' . $user_id);

    if ( ! $user_id ) {
        error_log('SAP Bulk Pricing: User not logged in');
        wp_send_json_error( 'User not logged in' );
        return;
    }

    $pricing_data = array();
    $debug_info = array();

    foreach ( $skus as $sku ) {
        $debug_info[$sku] = array('status' => 'processing');

        // Find product by SKU
        $product_id = wc_get_product_id_by_sku( $sku );
        if ( ! $product_id ) {
            $debug_info[$sku] = array('status' => 'product_not_found');
            error_log("SAP Bulk Pricing: Product not found for SKU: $sku");
            continue;
        }

        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            $debug_info[$sku] = array('status' => 'product_load_failed');
            error_log("SAP Bulk Pricing: Failed to load product for SKU: $sku");
            continue;
        }

        // Build SAP payload for this product
        $payload = hytec_build_sales_order_payload( $product, $user_id );
        if ( ! $payload ) {
            $debug_info[$sku] = array('status' => 'payload_build_failed');
            error_log("SAP Bulk Pricing: Failed to build payload for SKU: $sku");
            continue;
        }

        $debug_info[$sku]['payload'] = $payload;

        // Call SAP API
        $response = hytec_call_sap_sales_order( $payload );
        if ( is_wp_error( $response ) ) {
            $debug_info[$sku] = array('status' => 'sap_api_error', 'error' => $response->get_error_message());
            error_log("SAP Bulk Pricing: SAP API error for SKU $sku: " . $response->get_error_message());
            continue;
        }

        $debug_info[$sku]['response'] = $response;

        // Extract pricing data
        $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
        $first = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();

        if ( ! empty( $first ) ) {
            $net_value_raw = isset( $first['net_value'] ) ? $first['net_value'] : '';
            $sales_tax_raw = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';

            // Convert SAP response values: divide by 100 to get proper decimal format
            $net_value = '';
            $sales_tax = '';
            $formatted_net_value = '';
            $formatted_sales_tax = '';

            if ( $net_value_raw !== '' ) {
                $net_value = number_format( (float)$net_value_raw / 100, 2, '.', ',' );
                $formatted_net_value = get_woocommerce_currency_symbol() . number_format( (float)$net_value_raw / 100, 2, '.', ',' );
            }

            if ( $sales_tax_raw !== '' ) {
                $sales_tax = number_format( (float)$sales_tax_raw / 100, 2, '.', ',' );
                $formatted_sales_tax = get_woocommerce_currency_symbol() . number_format( (float)$sales_tax_raw / 100, 2, '.', ',' );
            }

            $pricing_data[ $sku ] = array(
                'net_value' => $net_value,
                'sales_tax' => $sales_tax,
                'formatted_net_value' => $formatted_net_value,
                'formatted_sales_tax' => $formatted_sales_tax
            );

            $debug_info[$sku]['status'] = 'success';
            error_log("SAP Bulk Pricing: Success for SKU $sku - Net: $formatted_net_value");
        } else {
            $debug_info[$sku]['status'] = 'no_pricing_data';
            error_log("SAP Bulk Pricing: No pricing data in response for SKU: $sku");
        }
    }

    error_log('SAP Bulk Pricing: Final result count: ' . count($pricing_data));

    wp_send_json_success( array(
        'pricing' => $pricing_data,
        'debug' => $debug_info
    ) );
}

// ============================================================================
// CART PAGE SAP PRICING INTEGRATION
// ============================================================================

// Add SAP NET price display to cart line items
if ( ! function_exists( 'hytec_add_sap_pricing_to_cart_item' ) ) {
    function hytec_add_sap_pricing_to_cart_item( $product_name, $cart_item, $cart_item_key ) {
        // Only show for logged in users
        if ( ! is_user_logged_in() ) {
            return $product_name;
        }

        $product = isset($cart_item['data']) ? $cart_item['data'] : null;
        if ( ! $product || ! ($product instanceof WC_Product) ) {
            return $product_name;
        }

        $user_id = get_current_user_id();

        // Get SAP pricing for this product with quantity = 1 (unit price only)
        $pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $product, $user_id, 1 );

        if ( is_array( $pricing ) && isset( $pricing['net_value'] ) && $pricing['net_value'] !== '' ) {
            // Convert SAP response (divide by 100 for proper decimal format)
            $net_value_decimal = number_format( (float)$pricing['net_value'] / 100, 2, '.', '' );

            // Format currency based on user's company code
            $company_code = get_user_meta($user_id, '_companycode', true);
            if ($company_code === '3090') {
                // US customers - show in USD
                $formatted_price = '$' . $net_value_decimal;
            } else {
                // Non-US customers - show in EUR
                $formatted_price = '€' . $net_value_decimal;
            }

            // Add NET price below the product name
            $product_name .= '<br><small style="color: #666; font-weight: normal;">NET: ' . $formatted_price . '</small>';
        }

        return $product_name;
    }

    // Hook into cart item name display (TEMPORARILY DISABLED)
    // add_filter( 'woocommerce_cart_item_name', 'hytec_add_sap_pricing_to_cart_item', 10, 3 );
}

// Enhanced pricing function that accepts quantity parameter
if ( ! function_exists( 'hytec_sap_get_or_fetch_pricing_with_quantity' ) ) {
    function hytec_sap_get_or_fetch_pricing_with_quantity( $product, $user_id, $quantity = 1 ) {
        if ( ! ( $product instanceof WC_Product ) ) return null;

        // Create cache key that includes quantity
        $cache_key = $product->get_id() . '|' . $quantity;
        $cached = hytec_sap_get_last_pricing( $cache_key );
        if ( is_array( $cached ) ) return $cached;

        // Build payload with specific quantity
        $payload = hytec_build_sales_order_payload( $product, $user_id, $quantity );
        if ( ! $payload ) return null;

        $response = hytec_call_sap_sales_order( $payload );
        if ( is_wp_error( $response ) ) return null;

        $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
        $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
        $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
        $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';

        $data = array( 'net_value' => $net_value, 'sales_tax' => $sales_tax, 'raw' => $response );
        hytec_sap_set_last_pricing( $cache_key, $data );
        return $data;
    }
}

// ============================================================================
// CART SUBTOTAL SAP PRICING REPLACEMENT
// ============================================================================

// Replace cart item subtotal with SAP pricing using WooCommerce filter
if ( ! function_exists( 'hytec_replace_cart_subtotal_with_sap' ) ) {
    function hytec_replace_cart_subtotal_with_sap( $subtotal, $cart_item, $cart_item_key ) {
        // Only show for logged in users
        if ( ! is_user_logged_in() ) {
            return $subtotal;
        }

        $product = isset($cart_item['data']) ? $cart_item['data'] : null;
        if ( ! $product || ! ($product instanceof WC_Product) ) {
            return $subtotal;
        }

        $user_id = get_current_user_id();
        $quantity = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

        // Get SAP pricing for this product with actual cart quantity
        $pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $product, $user_id, $quantity );

        if ( is_array( $pricing ) && isset( $pricing['net_value'] ) && $pricing['net_value'] !== '' ) {
            // Convert SAP response (divide by 100 for proper decimal format)
            $net_value_decimal = (float)$pricing['net_value'] / 100;

            // Format currency based on user's company code
            $company_code = get_user_meta($user_id, '_companycode', true);
            if ($company_code === '3090') {
                // US customers - show in USD
                $formatted_subtotal = '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">$</span>' . number_format($net_value_decimal, 2, '.', '') . '</bdi></span>';
            } else {
                // Non-US customers - show in EUR
                $formatted_subtotal = '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">€</span>' . number_format($net_value_decimal, 2, '.', '') . '</bdi></span>';
            }

            return $formatted_subtotal;
        }

        return $subtotal; // Fallback to original subtotal if SAP pricing not available
    }

    // Hook into cart item subtotal display with high priority
    add_filter( 'woocommerce_cart_item_subtotal', 'hytec_replace_cart_subtotal_with_sap', 20, 3 );
}

// Define missing SAP OAuth error function to prevent errors
if ( ! function_exists( 'hytec_sap_oauth_last_error' ) ) {
    function hytec_sap_oauth_last_error() {
        return '';
    }
}
