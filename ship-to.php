<?php
/**
 * Test script for SAP ShipTo Addresses API
 * This script tests the plugin functionality with sample data
 */

// Load WordPress
if (!function_exists('get_user_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>SAP ShipTo Addresses API Test</h1>\n";

echo "<h2>Address ID and SoldTo Lookup</h2>\n";
echo "<p><strong>Important:</strong> The new endpoint uses 'customerId' as the address_id.</p>\n";
echo "<p>Example: customerId '1221443' becomes address_id '1221443' in the database.</p>\n";
echo "<p><strong>SoldTo Lookup:</strong> The system will check wp_sap_soldto_customers.shiptos array to find the actual customer_id.</p>\n";

// Test data showing multiple addresses for the same customer
$test_data = [
    "shipTo" => [
        [
            "customerId" => "1131479",
            "companyCode" => "10001",
            "identifier" => "TEST NEW 1126268",
            "isDefaultAddress" => true,
            "companyName" => null,
            "country" => "US",
            "address" => [
                "street" => "Elm Street2",
                "apartment" => null,
                "city" => "Springwood 4",
                "postalCode" => "111"
            ],
            "stateCounty" => "NY"
        ],
        [
            "customerId" => "1126597",
            "companyCode" => "10001",
            "identifier" => "Secondary Warehouse",
            "isDefaultAddress" => false,
            "companyName" => null,
            "country" => "US",
            "address" => [
                "street" => "Pine Road",
                "apartment" => "Unit 5",
                "city" => "Springwood",
                "postalCode" => "12346"
            ],
            "stateCounty" => "NY"
        ]
    ]
];

echo "<h2>Test Data</h2>\n";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>\n";

// Check if we have a test user with customerId 1015088 (from soldto lookup)
echo "<h2>Checking for Test User</h2>\n";

$test_customer_id = '1015088'; // This should be the actual customer_id from soldto table
$test_user = get_users([
    'meta_key' => '_customer',
    'meta_value' => $test_customer_id,
    'number' => 1
]);

if (empty($test_user)) {
    echo "<p>❌ No user found with customerId '{$test_customer_id}'</p>\n";
    echo "<p>Creating a test user...</p>\n";

    // Create test user
    $user_data = [
        'user_login' => 'testuser_' . time(),
        'user_email' => 'test_' . time() . '@example.com',
        'user_pass' => wp_generate_password(),
        'role' => 'b2b_customer'
    ];

    $user_id = wp_insert_user($user_data);

    if (is_wp_error($user_id)) {
        echo "<p>❌ Failed to create test user: " . $user_id->get_error_message() . "</p>\n";
        exit;
    }

    // Add customer ID meta
    update_user_meta($user_id, '_customer', $test_customer_id);

    echo "<p>✅ Created test user with ID: {$user_id}</p>\n";
} else {
    $user_id = $test_user[0]->ID;
    echo "<p>✅ Found existing test user with ID: {$user_id}</p>\n";
}

// Show current addresses before processing
echo "<h2>Current Addresses (Before)</h2>\n";
$current_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
if (empty($current_addresses)) {
    echo "<p>No existing addresses</p>\n";
} else {
    echo "<pre>" . print_r($current_addresses, true) . "</pre>\n";
}

// Test the soldto lookup function
echo "<h2>Testing SoldTo Lookup</h2>\n";

if (function_exists('sap_shipto_lookup_customer_from_soldto')) {
    foreach ($test_data['shipTo'] as $index => $sap_address) {
        $address_id = $sap_address['customerId'] ?? 'N/A';
        echo "<h3>Address " . ($index + 1) . " - Lookup Test</h3>\n";
        echo "<p><strong>Looking up address_id:</strong> '{$address_id}'</p>\n";

        $found_customer_id = sap_shipto_lookup_customer_from_soldto($address_id);

        if ($found_customer_id) {
            echo "<p>✅ <strong>Found in SoldTo:</strong> customer_id '{$found_customer_id}'</p>\n";
        } else {
            echo "<p>❌ <strong>Not found in SoldTo:</strong> address_id '{$address_id}' not in any shiptos array</p>\n";
        }
    }
} else {
    echo "<p>❌ SoldTo lookup function not loaded.</p>\n";
}

// Test the conversion function
echo "<h2>Testing Address Conversion</h2>\n";

// Load the plugin functions
if (function_exists('convert_sap_to_wcmca_format')) {
    foreach ($test_data['shipTo'] as $index => $sap_address) {
        echo "<h3>Address " . ($index + 1) . "</h3>\n";
        echo "<h4>SAP Format:</h4>\n";
        echo "<pre>" . print_r($sap_address, true) . "</pre>\n";

        // Show the address_id mapping
        $customer_id = $sap_address['customerId'] ?? 'N/A';
        echo "<p><strong>Address ID Mapping:</strong> customerId '{$customer_id}' → address_id '{$customer_id}'</p>\n";

        $wcmca_format = convert_sap_to_wcmca_format($sap_address);

        echo "<h4>WCMCA Format:</h4>\n";
        echo "<pre>" . print_r($wcmca_format, true) . "</pre>\n";

        // Highlight the address_id in the result
        if (isset($wcmca_format['address_id'])) {
            echo "<p><strong>✅ Result address_id:</strong> '{$wcmca_format['address_id']}'</p>\n";
        }
    }
} else {
    echo "<p>❌ Plugin functions not loaded. Make sure the plugin is activated.</p>\n";
}

// Test the API endpoint via internal function call
echo "<h2>Testing API Processing</h2>\n";

if (function_exists('process_single_shipto_address')) {
    foreach ($test_data['shipTo'] as $index => $address) {
        echo "<h3>Processing Address " . ($index + 1) . "</h3>\n";
        
        $result = process_single_shipto_address($address);
        
        if (is_wp_error($result)) {
            echo "<p>❌ Error: " . $result->get_error_message() . "</p>\n";
        } else {
            echo "<p>✅ Success: " . print_r($result, true) . "</p>\n";
        }
    }
} else {
    echo "<p>❌ Plugin processing functions not loaded.</p>\n";
}

// Show final addresses after processing
echo "<h2>Final Addresses (After)</h2>\n";
$final_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
if (empty($final_addresses)) {
    echo "<p>No addresses found</p>\n";
} else {
    echo "<pre>" . print_r($final_addresses, true) . "</pre>\n";
    echo "<p>Total addresses: " . count($final_addresses) . "</p>\n";
}

// Test multiple addresses functionality
echo "<h2>Testing Multiple Addresses per Customer</h2>\n";

if (function_exists('sap_shipto_get_all_customer_addresses')) {
    // Test with customer_id 1015088 (should have multiple addresses)
    $customer_id = '1015088';
    echo "<h3>All Addresses for Customer: {$customer_id}</h3>\n";

    $all_addresses = sap_shipto_get_all_customer_addresses($customer_id);

    if ($all_addresses) {
        echo "<p><strong>✅ Found {$all_addresses['total_address_records']} address records for customer {$customer_id}</strong></p>\n";

        foreach ($all_addresses['addresses'] as $index => $address_record) {
            echo "<h4>Address Record " . ($index + 1) . " (address_id: {$address_record['address_id']})</h4>\n";
            echo "<p><strong>Default:</strong> " . ($address_record['has_default_address'] ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Updated:</strong> {$address_record['updated_at']}</p>\n";
            echo "<pre>" . print_r($address_record['addresses'], true) . "</pre>\n";
        }
    } else {
        echo "<p>❌ No addresses found for customer {$customer_id}</p>\n";
    }
} else {
    echo "<p>❌ Multiple addresses function not loaded.</p>\n";
}

// Test the REST API endpoint
echo "<h2>Testing REST API Endpoint</h2>\n";
echo "<p>You can test the REST API endpoint by sending a POST request to:</p>\n";
echo "<code>" . home_url('/wp-json/wc/v3/sap-shipto') . "</code>\n";
echo "<p>With the following JSON data:</p>\n";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>\n";

echo "<h2>cURL Command for Testing</h2>\n";
echo "<pre>";
echo "curl -X POST \\\n";
echo "  '" . home_url('/wp-json/wc/v3/sap-shipto') . "' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($test_data) . "'";
echo "</pre>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>Check the WordPress error logs for detailed processing information.</p>\n";
?>
