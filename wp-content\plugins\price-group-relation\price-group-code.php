<?php
/*
Plugin Name: Price Group & Code
Description: Manage the relationship between price codes and price groups for USD, EUR, and GBP.
Version: 1.0
Author: Y.Dev
*/

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Hook to add admin menu
add_action('admin_menu', 'price_group_code_menu');

// Create main menu and submenus for USD, EUR, and GBP
function price_group_code_menu()
{
    add_menu_page(
        'Price Group & Code',               // Page title
        'Price Group & Code',               // Menu title
        'manage_options',                   // Capability
        'price-group-code',                 // Menu slug
        'price_group_code_usd_page',        // Default to USD page
        'dashicons-tag',                    // Menu icon
        57                                  // Position in the menu
    );

    add_submenu_page(
        'price-group-code',                 // Parent slug
        'USD Price Group & Code',           // Page title
        'USD',                              // Menu title
        'manage_options',                   // Capability
        'price-group-code-usd',             // Submenu slug
        'price_group_code_usd_page'         // Callback function
    );

    add_submenu_page(
        'price-group-code',                 // Parent slug
        'EUR Price Group & Code',           // Page title
        'EUR',                              // Menu title
        'manage_options',                   // Capability
        'price-group-code-eur',
        'price_group_code_eur_page'         // Callback function
    );

    add_submenu_page(
        'price-group-code',                 // Parent slug
        'GBP Price Group & Code',           // Page title
        'GBP',                              // Menu title
        'manage_options',                    // Capability
        'price-group-code-gbp',                   // Capability
        'price_group_code_gbp_page'         // Callback function
    );
    remove_submenu_page('price-group-code', 'price-group-code');
}


// Include CRUD logic for each currency
require_once plugin_dir_path(__FILE__) . 'includes/usd.php';
require_once plugin_dir_path(__FILE__) . 'includes/eur.php';
require_once plugin_dir_path(__FILE__) . 'includes/gbp.php';
